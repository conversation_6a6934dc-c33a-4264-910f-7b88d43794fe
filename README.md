# Obsidian Sync Tools

Công cụ Python để đồng bộ notes và media từ Obsidian vault sang các nền tảng khác nhau như Microsoft OneNote và Google Docs/Drive.

## Tính năng

### Obsidian to OneNote Sync

- ✅ Đồng bộ tất cả markdown files từ Obsidian
- ✅ Chuyển đổi Obsidian markdown syntax sang HTML cho OneNote
- ✅ Hỗ trợ embedded images và media files
- ✅ Tự động tạo notebook và sections dựa trên cấu trúc thư mục
- ✅ Xử lý Obsidian links `[[link]]`
- ✅ Hỗ trợ tables, code blocks, và formatting

### Obsidian to Google Docs/Drive Sync

- ✅ Đồng bộ tất cả markdown files từ Obsidian
- ✅ Chuyển đổi Obsidian markdown sang Google Docs format
- ✅ Upload media files lên Google Drive và chèn trực tiếp vào Google Docs
- ✅ Tự động tạo folder structure trên Google Drive
- ✅ Hỗ trợ headers, lists, tables, code blocks
- ✅ OAuth2 authentication với Google

## <PERSON><PERSON><PERSON> cầu hệ thống

- Python 3.7+
- Microsoft Azure App Registration (cho OneNote sync)
- Google Cloud Project với APIs được kích hoạt (cho Google sync)
- Obsidian vault với markdown files

## Cài đặt

1. Clone repository:

```bash
git clone <repository-url>
cd sync
```

2. Cài đặt dependencies:

```bash
pip3 install -r requirements.txt
```

## Sử dụng

### OneNote Sync

1. Tạo config file:

```bash
python obsidian_to_onenote_sync.py --create-config
```

2. Chỉnh sửa `sync_config.json` với Microsoft App credentials

3. Chạy sync:

```bash
python obsidian_to_onenote_sync.py
# hoặc
make -f sync.mk sync
# hoặc
./run_sync.sh
```

### Google Docs/Drive Sync

1. Tạo config file:

```bash
python obsidian_to_google_sync.py --create-config
```

2. Download `credentials.json` từ Google Cloud Console

3. Chạy sync:

```bash
python obsidian_to_google_sync.py
# hoặc
make -f sync.mk sync-google
# hoặc
./run_google_sync.sh
```

## Makefile Commands

Sử dụng Makefile để thực hiện các tác vụ phổ biến:

```bash
make -f sync.mk help          # Hiển thị help
make -f sync.mk install       # Cài đặt dependencies
make -f sync.mk setup-example # Tạo Obsidian vault mẫu
make -f sync.mk create-config # Tạo config file
make -f sync.mk sync          # Chạy OneNote sync
make -f sync.mk sync-google   # Chạy Google Docs/Drive sync
make -f sync.mk clean         # Dọn dẹp files tạm
```

## Cấu trúc dự án

```
sync/
├── obsidian_to_onenote_sync.py    # OneNote sync module
├── obsidian_to_google_sync.py     # Google Docs/Drive sync module
├── requirements.txt               # Python dependencies
├── sync.mk                        # Makefile commands
├── run_sync.sh                    # OneNote sync runner script
├── run_google_sync.sh             # Google sync runner script
├── OBSIDIAN_SYNC_README.md        # OneNote sync documentation
├── GOOGLE_SYNC_README.md          # Google sync documentation
└── README.md                      # File này
```

## Logs và Troubleshooting

- OneNote sync: `sync.log`
- Google sync: `google_sync.log`

Xem các file README chi tiết:

- [OneNote Sync Guide](OBSIDIAN_SYNC_README.md)
- [Google Docs/Drive Sync Guide](GOOGLE_SYNC_README.md)

## Ví dụ sử dụng

### Tạo Obsidian vault mẫu

```bash
make -f sync.mk setup-example
```

### Sync toàn bộ vault

```bash
# OneNote
./run_sync.sh

# Google Docs/Drive
./run_google_sync.sh
```

## Hỗ trợ format

### Obsidian Markdown Features

- Headers (`# ## ###`)
- Bold và italic (`**bold**`, `*italic*`)
- Links (`[[internal link]]`, `[external](url)`)
- Images (`![[image.png]]`)
- Code blocks (`code`)
- Tables
- Lists (bullet và numbered)
- Embedded media files

### Platform-specific Features

**OneNote:**

- Tạo notebooks và sections
- HTML formatting
- Embedded images as base64

**Google Docs/Drive:**

- Folder structure mirroring
- Native Google Docs formatting
- Media files uploaded to Drive và chèn trực tiếp vào documents
- OAuth2 authentication
- Tự động tìm và xử lý hình ảnh từ các thư mục attachments

## Contributing

1. Fork repository
2. Tạo feature branch: `git checkout -b feature-name`
3. Thực hiện thay đổi
4. Test các thay đổi
5. Commit: `git commit -m "Add feature"`
6. Push: `git push origin feature-name`
7. Tạo pull request

## License

MIT License - xem file LICENSE để biết chi tiết.
