# Obsidian to Google Docs/Drive Sync Tool

Công cụ Python để đồng bộ toà<PERSON> bộ notes và media từ Obsidian vault sang Google Docs và Google Drive.

## Tính năng

- ✅ Đồng bộ tất cả markdown files từ Obsidian
- ✅ Chuyển đổi Obsidian markdown syntax sang Google Docs format
- ✅ Hỗ trợ embedded images và media files với tự động chèn vào Google Docs
- ✅ Tự động tạo folder structure trên Google Drive dựa trên cấu trúc thư mục Obsidian
- ✅ Xử lý Obsidian links `[[link]]`
- ✅ Hỗ trợ headers, lists, tables, code blocks, và formatting
- ✅ Upload media files lên Google Drive
- ✅ Logging chi tiết cho việc debug

## Yêu cầu hệ thống

- Python 3.7+
- Google Cloud Project với Google Drive API và Google Docs API được kích hoạt
- Google OAuth2 credentials
- Obsidian vault với markdown files

## Cài đặt

1. Clone hoặc download script
2. Cài đặt dependencies:

```bash
pip3 install -r requirements.txt
```

## Thiết lập Google Cloud Project

### Bước 1: Tạo Google Cloud Project

1. Truy cập [Google Cloud Console](https://console.cloud.google.com/)
2. Tạo project mới hoặc chọn project hiện có
3. Kích hoạt các APIs cần thiết:
   - Google Drive API
   - Google Docs API

### Bước 2: Tạo OAuth2 Credentials

1. Trong Google Cloud Console, đi tới **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth 2.0 Client ID**
3. Chọn **Desktop application**
4. Download file JSON và lưu thành `credentials.json`

### Bước 3: Cấu hình quyền truy cập

Đảm bảo OAuth consent screen đã được cấu hình và thêm các scopes:

- `https://www.googleapis.com/auth/drive`
- `https://www.googleapis.com/auth/documents`

## Sử dụng

### Bước 1: Tạo config file

```bash
python3 obsidian_to_google_sync.py --create-config
```

### Bước 2: Cấu hình

Chỉnh sửa `google_sync_config.json`:

```json
{
  "obsidian_path": "./obsidian",
  "credentials_path": "credentials.json",
  "root_folder_name": "Obsidian Sync",
  "excluded_folders": [".obsidian", ".trash"],
  "excluded_files": ["*.tmp", "*.bak"]
}
```

- `obsidian_path`: Đường dẫn đến Obsidian vault của bạn
- `credentials_path`: Đường dẫn đến file credentials.json
- `root_folder_name`: Tên folder gốc trong Google Drive

### Bước 3: Chạy sync

```bash
python3 obsidian_to_google_sync.py
```

Script sẽ:

1. Yêu cầu bạn authenticate với Google (lần đầu tiên)
2. Mở browser để đăng nhập Google
3. Sau khi authenticate, bắt đầu sync
4. Tạo folder structure trên Google Drive
5. Convert và upload tất cả notes

## Cấu trúc sync

```
Obsidian Vault/
├── folder1/
│   ├── note1.md
│   └── note2.md
├── folder2/
│   └── note3.md
├── attachments/
│   └── image.png
└── root_note.md

↓ Sync to ↓

Google Drive/
└── Obsidian Sync/
    ├── folder1/
    │   ├── note1 (Google Doc)
    │   └── note2 (Google Doc)
    ├── folder2/
    │   └── note3 (Google Doc)
    ├── Media/
    │   └── image.png
    └── root_note (Google Doc)
```

## Chuyển đổi format

| Obsidian Markdown | Google Docs                                |
| ----------------- | ------------------------------------------ |
| `# Header 1`      | Heading 1 style                            |
| `## Header 2`     | Heading 2 style                            |
| `**bold**`        | Bold text                                  |
| `*italic*`        | Italic text                                |
| `[[link]]`        | Internal link                              |
| `![[image.png]]`  | Uploaded image chèn trực tiếp vào document |
| Code blocks       | Monospace font với background              |
| Tables            | Text format (simplified)                   |
| Lists             | Bullet/numbered lists                      |

## Xử lý hình ảnh

Module đã được cải tiến để xử lý hình ảnh một cách tự động:

1. **Tìm kiếm hình ảnh**: Tự động tìm hình ảnh trong các thư mục:

   - Thư mục hiện tại của note
   - `attachments/`
   - `assets/`
   - `media/`

2. **Upload lên Google Drive**:

   - Tạo folder "Media" trong root folder
   - Upload hình ảnh với quyền public view
   - Tạo public URL cho embedding

3. **Chèn vào Google Docs**:

   - Thay thế syntax `![[image.png]]` bằng placeholder
   - Chèn hình ảnh thực tế vào document
   - Tự động resize (400x300 pt)

4. **Hỗ trợ format**:
   - PNG, JPG, JPEG, GIF, BMP, SVG
   - PDF files
   - Video files (MP4)
   - Audio files (MP3, WAV)

## Command Line Options

```bash
# Sử dụng cơ bản
python obsidian_to_google_sync.py

# Chỉ định đường dẫn Obsidian khác
python obsidian_to_google_sync.py --obsidian-path "/path/to/vault"

# Sử dụng config file khác
python obsidian_to_google_sync.py --config "my_config.json"

# Chỉ định credentials file khác
python obsidian_to_google_sync.py --credentials "my_credentials.json"

# Chỉ định tên root folder
python obsidian_to_google_sync.py --root-folder "My Notes"

# Tạo config file mới
python obsidian_to_google_sync.py --create-config
```

## Logs

Script tạo file log `google_sync.log` với thông tin chi tiết về quá trình sync.

## Troubleshooting

### Lỗi authentication

- Kiểm tra file `credentials.json` có tồn tại và đúng format
- Đảm bảo APIs đã được kích hoạt trong Google Cloud Console
- Xóa file `google_token.json` để authenticate lại

### Lỗi upload files

- Kiểm tra quyền truy cập Google Drive
- Đảm bảo có đủ storage space trên Google Drive
- Kiểm tra kết nối internet

### Lỗi convert markdown

- Kiểm tra encoding của markdown files (phải là UTF-8)
- Kiểm tra syntax markdown có hợp lệ
- Xem log file để biết chi tiết lỗi

### Lỗi Google Docs API

**Lỗi "Invalid JSON payload" với fontFamily:**

- Module đã tự động fix lỗi này
- Sử dụng `weightedFontFamily` thay vì `fontFamily`
- Request validation tự động chuyển đổi format

**Lỗi formatting requests:**

- Module có validation tự động
- Requests không hợp lệ sẽ bị skip
- Document vẫn được tạo nhưng có thể thiếu formatting

## Giới hạn

- Tables được convert thành text format đơn giản
- Một số Obsidian plugins syntax có thể không được hỗ trợ
- Google Docs có giới hạn về kích thước document
- Rate limiting của Google APIs có thể làm chậm quá trình sync với vault lớn

## Bảo mật

- File `credentials.json` chứa thông tin nhạy cảm, không share publicly
- File `google_token.json` được tạo tự động, cũng cần bảo mật
- Script chỉ yêu cầu quyền cần thiết (Drive và Docs access)

## Hỗ trợ

Nếu gặp vấn đề, hãy kiểm tra:

1. File log `google_sync.log`
2. Google Cloud Console để xem API quotas
3. Đảm bảo tất cả dependencies đã được cài đặt đúng
