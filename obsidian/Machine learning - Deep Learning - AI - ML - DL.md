---
relates:
  - "[[AI support for coding]]"
  - "[[6 <PERSON><PERSON><PERSON> lư<PERSON> Prompt Hi<PERSON><PERSON> quả của OpenAI]]"
  - "[[AI - Large Language Models (LLM)]]"
  - "[[MCP - Model Context Protocol]]"
tags:
  - machinelearning
  - deeplearning
---
# 1. Machine Learning, Deep Learning & AI

## 1.1. <PERSON><PERSON><PERSON> nguyên học tập

### 1.1.1. <PERSON><PERSON><PERSON><PERSON> học

- [DataCamp – <PERSON>h<PERSON><PERSON> học AI cho mọi trình độ](https://www.datacamp.com/learn/popular/ai): <PERSON><PERSON> cấp các khóa học tương tác về AI, bao gồm: <PERSON><PERSON><PERSON>, <PERSON> tổng hợp, <PERSON>t<PERSON><PERSON>, và đạo đức AI. Phù hợp cho người mới bắt đầu và có lộ trình học rõ ràng. #course
- [Microsoft – AI Agents for Beginners](https://github.com/microsoft/ai-agents-for-beginners): <PERSON><PERSON><PERSON><PERSON> họ<PERSON> gồm 10 bài học về cách xây dựng AI Agents. <PERSON>o gồm các chủ đề như: thi<PERSON><PERSON> kế agent, sử dụng công cụ, RAG, và triển khai sản phẩm. #course
- MLOps Marathon 2023 - YouTube: [https://www.youtube.com/playlist?list=PLvmLXlo5OR87Gifw5IT-YWllj67YHhaEw](https://www.youtube.com/playlist?list=PLvmLXlo5OR87Gifw5IT-YWllj67YHhaEw) #course #MLOps

### 1.1.2. Tài liệu

- [[AI - Large Language Models (LLM)]]

- [https://github.com/aishwaryanr/awesome-generative-ai-guide](https://github.com/aishwaryanr/awesome-generative-ai-guide) #generativeAI
- [https://github.com/GokuMohandas/Made-With-ML](https://github.com/GokuMohandas/Made-With-ML): Learn how to design, develop, deploy and iterate on production-grade ML applications. #ML #development #deployment
- [book_ML_color](https://super.myninja.ai/agents/b70180c0-e099-4561-9da4-3f3e0f5d6710): Machine learning cơ bản. #ML #basic
- Những đột phá mới nhất trong Machine Learning: [https://viblo.asia/u/MinhLeeDuc](https://viblo.asia/u/MinhLeeDuc) #ML #news
- DVC - Git for data or AI model: [https://viblo.asia/p/tim-hieu-dvc-phan-1-lam-quen-voi-mot-so-concept-co-ban-cua-dvc-EoW4oXBrJml](https://viblo.asia/p/tim-hieu-dvc-phan-1-lam-quen-voi-mot-so-concept-co-ban-cua-dvc-EoW4oXBrJml) #DVC #git
- Giới thiệu về RLHF - Self-Rewarding Language Models: [https://viblo.asia/p/llm-paper-reading-self-rewarding-language-models-tim-hieu-cach-llm-tu-nang-cap-chinh-no-gwd43jx3VX9](https://viblo.asia/p/llm-paper-reading-self-rewarding-language-models-tim-hieu-cach-llm-tu-nang-cap-chinh-no-gwd43jx3VX9) #RLHF #LLM
- ASR - Paper reading | Tìm hiểu mô hình Whisper - Chuyển đổi giọng nói sang lệnh: [https://viblo.asia/p/asr-paper-reading-tim-hieu-mo-hinh-whisper-2oKLnnl1LQO](https://viblo.asia/p/asr-paper-reading-tim-hieu-mo-hinh-whisper-2oKLnnl1LQO) #ASR #Whisper
- Bài toán Tự động sửa lỗi chính tả tiếng Việt: [https://freedium.cfd/https://medium.com/zalotech/s%E1%BB%ADa-l%E1%BB%97i-ch%C3%ADnh-t%E1%BA%A3-ti%E1%BA%BFng-vi%E1%BB%87t-3bb0990af931](https://freedium.cfd/https://medium.com/zalotech/s%E1%BB%ADa-l%E1%BB%97i-ch%C3%ADnh-t%E1%BA%A3-ti%E1%BA%BFng-vi%E1%BB%87t-3bb0990af931) #spellcheck #vietnamese
- WebLLM: Running LLMs in the Browser: [https://techhub.iodigital.com/articles/what-is-webllm](https://techhub.iodigital.com/articles/what-is-webllm) ([Frontend - Front-end](https://super.myninja.ai/agents/b70180c0-e099-4561-9da4-3f3e0f5d6710)) #WebLLM #browser
- Local attendtion: [https://viblo.asia/p/local-attention-trong-mo-hinh-hoc-sau-MkNLrWkbVgA](https://viblo.asia/p/local-attention-trong-mo-hinh-hoc-sau-MkNLrWkbVgA) #attention
- Reasoning LLM và Những Điều Thú Vị Mà Có Thể Bạn Đã Biết Phần 1: [https://viblo.asia/p/advanced-llm-reasoning-llm-va-nhung-dieu-thu-vi-ma-co-the-ban-da-biet-phan-1-WR5JR1wn4Gv](https://viblo.asia/p/advanced-llm-reasoning-llm-va-nhung-dieu-thu-vi-ma-co-the-ban-da-biet-phan-1-WR5JR1wn4Gv) #LLM #reasoning
- Reasoning LLM và Những Điều Thú Vị Mà Có Thể Bạn Đã Biết Phần 2: [https://viblo.asia/p/advanced-llm-reasoning-llm-va-nhung-dieu-thu-vi-ma-co-the-ban-da-biet-phan-2-aAY4q3GyLPw](https://viblo.asia/p/advanced-llm-reasoning-llm-va-nhung-dieu-thu-vi-ma-co-the-ban-da-biet-phan-2-aAY4q3GyLPw) #LLM #reasoning

## 1.2. Ứng dụng AI SaaS

### 1.2.1. Tổng hợp

- OpenFuture - The World-Largest AI Tools Directory in 2023: [https://openfuture.ai](https://openfuture.ai/) #directory

### 1.2.2. Document

- [https://afforai.com](https://afforai.com/) #document
- Tìm thông tin trong các báo cáo khoa học: [https://elicit.org](https://elicit.org/) #research
- [https://lightpdf.com](https://lightpdf.com/) #pdf
- [https://www.chatpdf.com](https://www.chatpdf.com/) #pdf #chat
- Hemingway Editor: Công cụ đánh giá độ đọc và dễ hiểu của văn bản, Hemingway Editor sử dụng AI để đề xuất các cải tiến trong cấu trúc câu và phong cách viết. [https://hemingwayapp.com](https://hemingwayapp.com/) #writing #editor

## 1.3. Tools / Libraries / Frameworks

- The easiest way to serve AI apps and models: [https://github.com/bentoml/BentoML](https://github.com/bentoml/BentoML)

### 1.3.1. Slide

- [https://www.prezo.ai](https://www.prezo.ai/) #presentation
- [https://tome.app](https://tome.app/) #presentation

### 1.3.2. Media

#### *******. Video

- Chuyển văn bản thành video:
    - [runwayml.com](http://runwayml.com/) #text2video
- Tìm video bằng text: [twelvelabs.io](http://twelvelabs.io/) #videoSearch
- [Tạo video bằng các tool AI](https://super.myninja.ai/agents/b70180c0-e099-4561-9da4-3f3e0f5d6710)
- v-express - Công nghệ nhép miệng theo voice: [https://github.com/tiankuan93/ComfyUI-V-Express](https://github.com/tiankuan93/ComfyUI-V-Express) #lipSync #ComfyUI
- Chuyển hình ảnh + âm thanh của một người thành video của người đó: [https://humanaigc.github.io/emote-portrait-alive](https://humanaigc.github.io/emote-portrait-alive) #emote
- Biến ảnh của nhân vật nào đó thành video với biểu cảm của input video khác: [https://huggingface.co/spaces/KwaiVGI/LivePortrait](https://huggingface.co/spaces/KwaiVGI/LivePortrait) #livePortrait

#### *******. Image

- Chuyển văn bản thành hình ảnh:
    - [https://stockimg.ai](https://stockimg.ai/) #text2image
    - [https://www.unstability.ai](https://www.unstability.ai/) #text2image
    - [https://stablecog.com/generate](https://stablecog.com/generate) #text2image
    - [https://github.com/jakowenko/phrame](https://github.com/jakowenko/phrame) #text2image
    - [https://imgcreator.zmo.ai](https://imgcreator.zmo.ai/) #text2image
    - [https://designer.microsoft.com](https://designer.microsoft.com/) #text2image
    - [https://www.shakker.ai](https://www.shakker.ai/) #text2image
    - [https://gamma.app](https://gamma.app/)
- Vẽ design: [https://www.visily.ai](https://www.visily.ai/) #design
- Thay đồ cho model: [https://huggingface.co/spaces/yisol/IDM-VTON](https://huggingface.co/spaces/yisol/IDM-VTON) #clothing
- IC-Light (Relighting with Foreground Condition) - Thay đổi ánh sáng vật thể dựa vào background: [https://huggingface.co/spaces/lllyasviel/IC-Light](https://huggingface.co/spaces/lllyasviel/IC-Light) #relighting
- Mặc đồ cho model:
    - [https://github.com/speedTD/AI-ClothingTryOn](https://github.com/speedTD/AI-ClothingTryOn) #clothing
    - [https://www.google.com/shopping/tryon?gl=us&hl=en&utm_source=aim_email&utm_content=sl3&udm=28](https://www.google.com/shopping/tryon?gl=us&hl=en&utm_source=aim_email&utm_content=sl3&udm=28)
- Chỉnh sửa hình:
    - [https://github.com/VectorSpaceLab/OmniGen2](https://github.com/VectorSpaceLab/OmniGen2)

#### 1.3.2.3. Model

- Tạo avatar 3D: [https://avaturn.me](https://avaturn.me/) #avatar
- Tạo mô hình từ hình ảnh / video: [https://rerun.io](https://rerun.io/) #3dmodel

#### 1.3.2.4. Audio

- Chuyển văn bản thành giọng nói:
    - [https://vmixvoice.net](https://vmixvoice.net/) #text2speech
    - [https://bevoice.net](https://bevoice.net/) #text2speech
    - [https://elevenlabs.io](https://elevenlabs.io/) #text2speech
    - [https://github.com/resemble-ai/chatterbox](https://github.com/resemble-ai/chatterbox)
- Chuyển chữ thành nhạc:
    - [https://github.com/facebookresearch/audiocraft](https://github.com/facebookresearch/audiocraft) #text2music
    - [https://github.com/GrandaddyShmax/audiocraft_plus](https://github.com/GrandaddyShmax/audiocraft_plus) #text2music
    - [https://suno.com](https://suno.com/) #text2music

#### 1.3.2.5. Text

- Chuyển giọng nói thành văn bản:
    - [https://transcribe.com](https://transcribe.com/) #speech2text
    - [https://bevoice.net](https://bevoice.net/) #speech2text
- Tạo tóm tắt cho metting:
    - [https://insight7.io](https://insight7.io/) #summary
    - [https://tldv.io](https://tldv.io/) #summary

### 1.3.3. Coding

- [https://github.com/microsoft/NLWeb](https://github.com/microsoft/NLWeb)
- Tạo giao diện cho website:
    - [https://library.relume.io](https://library.relume.io/) #webUI
    - Tool tương tự figma: [https://github.com/onlook-dev/onlook](https://github.com/onlook-dev/onlook)
    - [https://same.new](https://same.new/)

### 1.3.4. Khác

- Tuning models:
    - Zero-shot Identity-Preserving Generation - Gen hình ảnh giữ lại các đặc điểm khuôn mặt đầu vào: [https://github.com/InstantID/InstantID](https://github.com/InstantID/InstantID) #imageGeneration

### 1.3.5. Stocks

- Civitai | Stable Diffusion models, embeddings, LoRAs and more: [https://civitai.com](https://civitai.com/) #stablediffusion #models

## 1.4. Công cụ (Tools)

- Data labeling: [https://doccano.github.io/doccano](https://doccano.github.io/doccano) #dataLabeling
- Get your documents ready for gen AI: https://github.com/docling-project/docling

### 1.4.1. Memory management

#### 1.4.1.1. Supermemory.ai

**Supermemory.ai** là nền tảng API ký ức (Memory API) dành cho thời đại AI, nhằm xây dựng một lớp ghi nhớ thông minh và bền vững giúp các sản phẩm AI hoặc cá nhân tổ chức, quản lý và tận dụng tối đa dữ liệu của chính mình một cách hiệu quả. Supermemory thường được ví như “bộ não thứ hai” số hóa cho mỗi người dùng hoặc hệ AI hiện đại[1](https://github.com/supermemoryai/supermemory)[2](https://playbooks.com/mcp/supermemory).

##### 1.4.1.1.1. Tính năng nổi bật

- **Semantic & AI Search**: Tìm kiếm theo ý nghĩa, không chỉ đơn thuần so khớp từ khóa. Điều này giúp truy xuất thông tin trở nên thông minh, liên kết ngữ cảnh tốt hơn giữa các tài liệu hay dữ liệu đã lưu[1](https://github.com/supermemoryai/supermemory)[3](https://supermemory.ai/blog/the-wow-factor-of-memory-how-flow-used-supermemory-to-build-smarter-stickier-products/)[2](https://playbooks.com/mcp/supermemory).
- **Content Handling đa dạng**: Lưu trữ và phân tích nhiều loại nội dung: văn bản, URL, PDF, hình ảnh, video. Tất cả được chuyển đổi và gắn metadata để tìm kiếm sau này[1](https://github.com/supermemoryai/supermemory)[3](https://supermemory.ai/blog/the-wow-factor-of-memory-how-flow-used-supermemory-to-build-smarter-stickier-products/).
- **Tăng cường trả lời cho AI (AI Model Enhancer)**: Kết nối API với LLM như OpenAI, Claude, Mistral... Dữ liệu của bạn được chọn lọc thông minh đưa vào ngữ cảnh hội thoại để tăng chất lượng trả lời của AI, giảm “hallucination”[1](https://github.com/supermemoryai/supermemory)[3](https://supermemory.ai/blog/the-wow-factor-of-memory-how-flow-used-supermemory-to-build-smarter-stickier-products/).
- **Tích hợp đa nền tảng**: Cho phép đồng bộ và tích hợp sâu với các công cụ Notion, Google Drive, CRM, mạng xã hội (Telegram, Twitter, WhatsApp...), các công cụ automations (Zapier), Chrome extension... Từ đó, mọi đội nhóm đều tận dụng được lớp memory mạnh mẽ ngay trong quy trình hiện tại4[5](https://www.b12.io/ai-directory/supermemory/)[3](https://supermemory.ai/blog/the-wow-factor-of-memory-how-flow-used-supermemory-to-build-smarter-stickier-products/)[2](https://playbooks.com/mcp/supermemory).
- **Tổ chức, lọc & phân loại thông tin**: Tích hợp advanced filters, lưu trữ metadata/partition, hỗ trợ chia sẻ giữa người dùng, xây dựng tổ chức dữ liệu lớn cho doanh nghiệp hoặc nhóm học tập[1](https://github.com/supermemoryai/supermemory)[2](https://playbooks.com/mcp/supermemory)[6](https://logicballs.com/ai-tools/supermemory).
- **Tốc độ & Mở rộng**: Thiết kế để đáp ứng hàng tỷ điểm dữ liệu với truy vấn thời gian thực dưới 400ms, phù hợp cả doanh nghiệp lớn lẫn cá nhân sáng tạo[3](https://supermemory.ai/blog/the-wow-factor-of-memory-how-flow-used-supermemory-to-build-smarter-stickier-products/).
- **Giao diện canvas 2D và Writing Assistant**: Sắp xếp nội dung trực quan, hỗ trợ Markdown, có công cụ gợi ý viết nội dung tận dụng knowledge base đã tích lũy[2](https://playbooks.com/mcp/supermemory)[7](https://supermemory.ai/blog).
- **Quyền kiểm soát, riêng tư, bảo mật**: Hỗ trợ triển khai cloud, on-premises, hoặc on-device. API và công nghệ đảm bảo an toàn dữ liệu, kiểm soát “ở đâu, như thế nào” tuỳ ý khách hàng4[5](https://www.b12.io/ai-directory/supermemory/)[3](https://supermemory.ai/blog/the-wow-factor-of-memory-how-flow-used-supermemory-to-build-smarter-stickier-products/).

##### 1.4.1.1.2. Các usecase tiêu biểu

| Usecase                       | Mô tả ứng dụng                                                                                                                                                                                                                                                                                                                 |
| ----------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| Trợ lý AI cá nhân             | Lưu & quản lý bookmark, tài liệu, ý tưởng, ghi chú…; hỏi lại thông tin nhanh chóng[2](https://playbooks.com/mcp/supermemory)[8](https://dessign.net/supermemory-ai/)                                                                                                                                                           |
| Nâng cấp chatbot nội bộ       | Chatbot hoặc AI agent có thể truy xuất tri thức doanh nghiệp, nội dung đào tạo, tài liệu khách hàng[1](https://github.com/supermemoryai/supermemory)[3](https://supermemory.ai/blog/the-wow-factor-of-memory-how-flow-used-supermemory-to-build-smarter-stickier-products/)[9](https://supermemory.ai/docs/overview/use-cases) |
| Assistants domain-specific    | Trợ lý xử lý email, họp, lịch, cộng tác viên ngành pháp lý, tài chính, y tế… với kiến thức chuyên biệt[9](https://supermemory.ai/docs/overview/use-cases)[2](https://playbooks.com/mcp/supermemory)                                                                                                                            |
| Tìm kiếm thông minh           | Smart search đề xuất sản phẩm, matching nghiên cứu học thuật, đối sánh hợp đồng[9](https://supermemory.ai/docs/overview/use-cases)                                                                                                                                                                                             |
| Quản lý tri thức doanh nghiệp | Knowledge hub tập trung, onboard nhân sự mới; tìm thông tin, quy trình nội bộ nhanh chóng[9](https://supermemory.ai/docs/overview/use-cases)[3](https://supermemory.ai/blog/the-wow-factor-of-memory-how-flow-used-supermemory-to-build-smarter-stickier-products/)                                                            |
| Giáo dục & Study tools        | Sinh viên/giáo viên lưu lecture, tài liệu, flashcard, quiz cá nhân hóa…[9](https://supermemory.ai/docs/overview/use-cases)[2](https://playbooks.com/mcp/supermemory)                                                                                                                                                           |
| Chatbot cộng đồng             | Giải đáp câu hỏi dựa vào lịch sử chat/forum cộng đồng; giảm tải cho đội ngũ CSKH[9](https://supermemory.ai/docs/overview/use-cases)[5](https://www.b12.io/ai-directory/supermemory/)                                                                                                                                           |
| Viết nội dung & Brand voice   | Gợi ý phong cách, giữ nhất quán thương hiệu, viết nội dung từ tri thức nền sẵn có[9](https://supermemory.ai/docs/overview/use-cases)                                                                                                                                                                                           |
| Quản lý dữ liệu y tế          | Trích xuất thông tin bệnh án, tóm tắt lịch sử, hỗ trợ ra quyết định lâm sàng bằng AI[9](https://supermemory.ai/docs/overview/use-cases)                                                                                                                                                                                        |
| Pháp lý & Compliance          | Tìm kiếm case law, điều khoản hợp đồng, trích xuất nghĩa vụ, bám sát cập nhật pháp lý[9](https://supermemory.ai/docs/overview/use-cases)                                                                                                                                                                                       |

##### 1.4.1.1.3. Ưu điểm

- **Truy xuất nhanh, mở rộng vượt trội**: Sub-400ms cho truy vấn lớn, thích hợp từ cá nhân tới doanh nghiệp[3](https://supermemory.ai/blog/the-wow-factor-of-memory-how-flow-used-supermemory-to-build-smarter-stickier-products/).
- **Tính mở và linh hoạt**: Kết nối gần như mọi nguồn dữ liệu; Model-agnostic cho phép thay đổi LLM linh hoạt mà không mất “memory”[1](https://github.com/supermemoryai/supermemory)[3](https://supermemory.ai/blog/the-wow-factor-of-memory-how-flow-used-supermemory-to-build-smarter-stickier-products/)[2](https://playbooks.com/mcp/supermemory).
- **Tăng chất lượng AI**: Giảm “hallucination”, nâng cao độ chính xác trả lời vì AI được tiếp cận “memory” giàu ngữ cảnh[1](https://github.com/supermemoryai/supermemory).
- **Bảo mật và kiểm soát mạnh**: Tùy biến triển khai (cloud/on-premise), linh hoạt quyền kiểm soát dữ liệu[3](https://supermemory.ai/blog/the-wow-factor-of-memory-how-flow-used-supermemory-to-build-smarter-stickier-products/).
- **Giao diện tiện dụng**: Dễ dùng cả lập trình viên lẫn người không chuyên, hỗ trợ canvas trực quan và assistant hỗ trợ viết[2](https://playbooks.com/mcp/supermemory)[7](https://supermemory.ai/blog).
- **Tích hợp đa dụng**: Kết nối đa công cụ, dễ dàng đưa vào quy trình doanh nghiệp hoặc sử dụng cá nhân[5](https://www.b12.io/ai-directory/supermemory/)[3](https://supermemory.ai/blog/the-wow-factor-of-memory-how-flow-used-supermemory-to-build-smarter-stickier-products/)[2](https://playbooks.com/mcp/supermemory).
- **Tiết kiệm chi phí**: Với AI, memory này giảm chi phí API nhờ tối ưu prompt và giảm context lặp lại[10](https://supermemory.ai/).

## 1.5. Nhược điểm

- **Yêu cầu kết nối internet liên tục**: Một số tính năng phụ thuộc vào kết nối mạng ổn định, chưa tối ưu cho offline hoàn toàn[11](https://supermemory.ai/blog/memory-engine/).
- **Thiết lập ban đầu**: Nếu chọn self-host hoặc tùy chỉnh sâu, cần kiến thức kỹ thuật, có thể gây khó khăn cho người không chuyên[11](https://supermemory.ai/blog/memory-engine/).
- **Tích hợp bên thứ ba giới hạn**: Một số tích hợp non-social platforms vẫn đang mở rộng thêm, chưa hoàn toàn phủ hết nhu cầu đặc biệt của tổ chức lớn[11](https://supermemory.ai/blog/memory-engine/).
- **Độ khó khi tiếp cận tính năng nâng cao**: Dù giao diện trực quan, các tính năng tuỳ chỉnh sâu sẽ tốn thời gian làm quen ban đầu[11](https://supermemory.ai/blog/memory-engine/).
- **Có khả năng chậm vào giờ cao điểm**: Khi lượng người dùng tăng đột biến, có thể xảy ra độ trễ[11](https://supermemory.ai/blog/memory-engine/).

## 1.6. Tổng kết

Supermemory.ai là một giải pháp memory API hiện đại, đa năng phù hợp cho cả cá nhân và doanh nghiệp muốn xây dựng kho tri thức, tăng hiệu suất AI, truy xuất thông tin nhanh và bảo mật vượt trội. Đây là xu hướng mới giúp AI không chỉ thông minh mà còn... _nhớ lâu, hiểu ngữ cảnh_!  
Tùy nhu cầu sử dụng và đặc thù dữ liệu, cần cân nhắc giữa khả năng mở rộng, bảo mật so với các hạn chế về tích hợp và kỹ thuật khi triển khai sâu hệ thống này[1](https://github.com/supermemoryai/supermemory)[3](https://supermemory.ai/blog/the-wow-factor-of-memory-how-flow-used-supermemory-to-build-smarter-stickier-products/)[2](https://playbooks.com/mcp/supermemory)[11](https://supermemory.ai/blog/memory-engine/).

1. [https://github.com/supermemoryai/supermemory](https://github.com/supermemoryai/supermemory)
2. [https://playbooks.com/mcp/supermemory](https://playbooks.com/mcp/supermemory)
3. [https://supermemory.ai/blog/the-wow-factor-of-memory-how-flow-used-supermemory-to-build-smarter-stickier-products/](https://supermemory.ai/blog/the-wow-factor-of-memory-how-flow-used-supermemory-to-build-smarter-stickier-products/)
4. [https://www.youtube.com/watch?v=f_RxlhPxdCI](https://www.youtube.com/watch?v=f_RxlhPxdCI)
5. [https://www.b12.io/ai-directory/supermemory/](https://www.b12.io/ai-directory/supermemory/)
6. [https://logicballs.com/ai-tools/supermemory](https://logicballs.com/ai-tools/supermemory)
7. [https://supermemory.ai/blog](https://supermemory.ai/blog)
8. [https://dessign.net/supermemory-ai/](https://dessign.net/supermemory-ai/)
9. [https://supermemory.ai/docs/overview/use-cases](https://supermemory.ai/docs/overview/use-cases)
10. [https://supermemory.ai](https://supermemory.ai/)
11. [https://supermemory.ai/blog/memory-engine/](https://supermemory.ai/blog/memory-engine/)
12. [https://docs.supermemory.ai/overview/use-cases](https://docs.supermemory.ai/overview/use-cases)
13. [https://www.simplilearn.com/advantages-and-disadvantages-of-artificial-intelligence-article](https://www.simplilearn.com/advantages-and-disadvantages-of-artificial-intelligence-article)
14. [https://www.tiktok.com/@wyzer.ai/video/7478802769118252318](https://www.tiktok.com/@wyzer.ai/video/7478802769118252318)
15. [https://supermemory.ai/docs/introduction](https://supermemory.ai/docs/introduction)
16. [https://supermemory.ai/blog/tag/case-study/](https://supermemory.ai/blog/tag/case-study/)
17. [https://supermemo.guru/wiki/Artificial_intelligence_needs_to_sleep](https://supermemo.guru/wiki/Artificial_intelligence_needs_to_sleep)
18. [https://topai.tools/t/super-memory-ai](https://topai.tools/t/super-memory-ai)
19. [https://www.linkedin.com/posts/seokeralaindia_ai-artificialintelligence-memory-activity-7262814619264573440-88wZ](https://www.linkedin.com/posts/seokeralaindia_ai-artificialintelligence-memory-activity-7262814619264573440-88wZ)
20. [https://www.reddit.com/r/AppIdeas/comments/1f0ykm2/supermemory_the_app_that_lets_you_search_your/](https://www.reddit.com/r/AppIdeas/comments/1f0ykm2/supermemory_the_app_that_lets_you_search_your/)

### 1.6.1. GUI

- ComfyUI - The most powerful and modular diffusion model GUI, api and backend with a graph/nodes interface: [https://github.com/comfyanonymous/ComfyUI](https://github.com/comfyanonymous/ComfyUI) #ComfyUI
    - Xoay ảnh 3D từ ảnh tĩnh: [https://www.facebook.com/watch/?ref=saved&v=1241934220105948](https://www.facebook.com/watch/?ref=saved&v=1241934220105948)

## 1.7. Nhận dạng (Recognition)

- [AWS Serverless] - Sử dụng Golang và Amazon Rekognition để xây dựng API tìm kiếm khuôn mặt bằng hình ảnh: [https://viblo.asia/p/aws-serverless-su-dung-golang-va-amazon-rekognition-de-xay-dung-api-tim-kiem-khuon-mat-bang-hinh-anh-yZjJY96XJOE](https://viblo.asia/p/aws-serverless-su-dung-golang-va-amazon-rekognition-de-xay-dung-api-tim-kiem-khuon-mat-bang-hinh-anh-yZjJY96XJOE) #AWS #Rekognition #faceRecognition
- Face recognition: [https://github.com/ageitgey/face_recognition](https://github.com/ageitgey/face_recognition) #faceRecognition
- Open vocabulary object detection - xu hướng mới trong phát hiện đối tượng: [https://viblo.asia/p/open-vocabulary-object-detection-xu-huong-moi-trong-phat-hien-doi-tuong-aNj4vDAoL6r](https://viblo.asia/p/open-vocabulary-object-detection-xu-huong-moi-trong-phat-hien-doi-tuong-aNj4vDAoL6r) #objectDetection

## 1.8. OCR (Optical Character Recognition)

- SVTR NET - Lời giải hoàn hảo cho bài toán OCR ? (viblo.asia): [https://viblo.asia/p/svtr-net-loi-giai-hoan-hao-cho-bai-toan-ocr-924lJg3a5PM](https://viblo.asia/p/svtr-net-loi-giai-hoan-hao-cho-bai-toan-ocr-924lJg3a5PM) #SVTR #OCR

## 1.9. Convolutional Neural Networks (CNN)

- [paper explain] Scaling Up Your Kernels to 31x31: Sự trở lại mạnh mẽ của CNN trên đường đua ImageNet (viblo.asia): [https://viblo.asia/p/paper-explain-scaling-up-your-kernels-to-31x31-su-tro-lai-manh-me-cua-cnn-tren-duong-dua-imagenet-GAWVpd8kV05](https://viblo.asia/p/paper-explain-scaling-up-your-kernels-to-31x31-su-tro-lai-manh-me-cua-cnn-tren-duong-dua-imagenet-GAWVpd8kV05) #CNN #ImageNet
- [https://viblo.asia/p/sam-giai-thuat-toi-uu-dang-dan-duoc-ung-dung-rong-rai-aNj4vXNxL6r](https://viblo.asia/p/sam-giai-thuat-toi-uu-dang-dan-duoc-ung-dung-rong-rai-aNj4vXNxL6r) #CNN

## 1.10. Midjourney

- Giới thiệu về Midjourney: Hướng dẫn cách viết Prompt - Viblo: [https://viblo.asia/p/gioi-thieu-ve-midjourney-huong-dan-cach-viet-prompt-vlZL9bA7VQK?fbclid=IwAR0knqUF5Nl2NSxZ2wTClk4hM32AuTTzr-1alwOvgpOfyIcNCx2jvVYFg-s](https://viblo.asia/p/gioi-thieu-ve-midjourney-huong-dan-cach-viet-prompt-vlZL9bA7VQK?fbclid=IwAR0knqUF5Nl2NSxZ2wTClk4hM32AuTTzr-1alwOvgpOfyIcNCx2jvVYFg-s) #Midjourney #prompt

## 1.11. Stable Diffusion

- Kho checkpoint, lora: [https://civitai.com](https://civitai.com/) #models #LoRA
- WebUI: [webui.graviti.com](http://webui.graviti.com/) #WebUI
- [Promt](https://super.myninja.ai/agents/b70180c0-e099-4561-9da4-3f3e0f5d6710)
- [Râm Generation](https://super.myninja.ai/agents/b70180c0-e099-4561-9da4-3f3e0f5d6710)
- TRANG CHỦ: [https://stablediffusion.life](https://stablediffusion.life/)
- Hugging Face – The AI community building the future.: [https://huggingface.co](https://huggingface.co/)
- Logic cơ bản trong việc tạo prompt trong stable diffusion: [https://www.youtube.com/watch?v=JGbzIC_KHrg](https://www.youtube.com/watch?v=JGbzIC_KHrg)
- EliAI: [https://eliai.vn](https://eliai.vn/)
- Bộ công cụ - Stable Diffusion Việt Nam: [https://stablediffusion.vn/bo-cong-cu](https://stablediffusion.vn/bo-cong-cu)
- Tài liệu: [https://sdcoban.vn/ebooks](https://sdcoban.vn/ebooks)
- [https://github.com/AUTOMATIC1111/stable-diffusion-webui](https://github.com/AUTOMATIC1111/stable-diffusion-webui)
- How to Run Stable Diffusion Locally With a GUI on Windows: [https://www.howtogeek.com/832491/how-to-run-stable-diffusion-locally-with-a-gui-on-windows](https://www.howtogeek.com/832491/how-to-run-stable-diffusion-locally-with-a-gui-on-windows)

### 1.11.1. Prompt

- Hướng dẫn viết promts:
    - [[ba3a5f41c1fb23e613f01b2c559113d1_MD5.pdf]]
    - [[f3ea3cd9399592466c19314ad4642e77_MD5.pdf]]
