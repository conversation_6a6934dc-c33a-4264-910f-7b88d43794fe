---
relates:
  - "[[AI support for coding]]"
  - "[[AI - Large Language Models (LLM)]]"
  - "[[Machine learning - Deep Learning - AI - ML - DL]]"
---
# 1. Resources

- https://github.com/modelcontextprotocol/servers

# 2. MCP registry

- Playwright MCP: https://github.com/microsoft/playwright-mcp
- Coinbase agentkit: https://github.com/coinbase/agentkit
- Open Source Generic MCP Client for testing & evaluating mcp servers and agents: https://github.com/Flux159/mcp-chat
- https://smithery.ai
- https://cursor.directory
- https://mastra.ai/mcp-registry-registry

# 3. Coding CLI

- Codex: https://github.com/openai/codex

## 3.1. Openhands

**Tương đương với lập trình viên con người:** AI agents của OpenHands **có thể làm bất cứ điều gì mà một lập trình viên con người có thể làm**, bao gồm:

- **Chỉnh sửa mã nguồn** (modifying code)
- **<PERSON><PERSON><PERSON> các l<PERSON>nh** (running commands)
- **Tìm kiếm thông tin trên web** (browsing the web)

# 4. Automate MCP

- browsermcp - Interact with browser: https://github.com/browsermcp/mcp

# 5. Context storage

- Up-to-date documentation for LLMs and AI code editors: https://context7.com

# 6. Cursor IDE

## 6.1. Resources

- https://cursor.directory

# 7. Browser MCP

- https://github.com/BrowserMCP/mcp