---
relates:
  - "[[<PERSON>O<PERSON>]]"
  - "[[Backend - Back-end]]"
  - "[[<PERSON><PERSON> chung IT]]"
---
# 1. Resources

- 26 Top Kubernetes Tools for 2024: https://medium.com/spacelift/26-top-kubernetes-tools-for-2024-6809b2f0d5d4
- Ingress: <PERSON>u<PERSON><PERSON> lý traffic từ ngoài vào.
- Kubernetes Example - Xây dựng hệ thống microservices đơn giản: https://viblo.asia/p/kubernetes-example-xay-dung-he-thong-microservices-don-gian-pgjLNKyAV32
- Công cụ hỗ trợ bảo mật k8s: https://viblo.asia/p/bao-mat-ung-dung-tren-kubernetes-he-dieu-hanh-may-khong-danh-cho-nguoi-mong-mo-Yym40GddV91

# 2. Tools

- https://tilt.dev: C<PERSON>ng cụ mã nguồn mở giú<PERSON> các nhóm phát triển phần mềm dễ dàng thiết lập và quản lý môi trường phát triển cho các ứng dụng microservices chạy trên Kubernetes. Nó tự động hóa các bước từ việc thay đổi mã nguồn đến triển khai ứng dụng, giúp tăng tốc độ phát triển và giảm thiểu lỗi.