---
number headings: auto, first-level 1, max 6, 1.1
---
# 1 Resources

- FrankenPHP and Laravel Octane with Docker:
	- https://chriswhite.is/coding/frankenphp-and-laravel-octane-with-docker
	- https://github.com/jaygaha/laravel-11-frankenphp-docker
- Code Laravel làm sao cho chuẩn? - <PERSON> Blog (chungnguyen.xyz)
- Giảm phụ thuộc ở layer Service của Laravel bằng cách sử dụng CommandBus: Laravel Việt Nam | Hi anh em, lại là mình đây :D | Facebook
- Cải thiện performance for eloquent: https://eloquent-course.reinink.ca
- Test Driven Design in Laravel: https://course.testdrivenlaravel.com
- Mục lục các bước scale dự án Laravel: https://courses.serversforhackers.com/scaling-laravel
- Enterprise Laravel
- Laravel Artisan Cheatsheet
- https://github.com/Nghiait123456/DissectLaravel
- C<PERSON><PERSON> câu hỏi phỏng vấn
- Feature packages in Laravel: https://bestlaravel.com
- Spatie Opensource Projects: https://spatie.be/open-source/projects
- [[Các câu hỏi phỏng vấn Laravel]]
- Design patterns, architects, structures and more for Laravel by Martin Joo: https://martinjoo.dev
- Managing Large Datasets in Laravel with LazyCollection: https://laravel-news.com/managing-large-datasets-in-laravel-with-lazycollection

# 2 Monitors / Debuggers

- Pulse: https://github.com/laravel/pulse
- https://github.com/itsgoingd/clockwork
- https://github.com/laradumps/laradumps
- https://github.com/laravel/telescope
- Laravel Horizon - Queue monitor

# 3 Front-end

- Turbo Laravel
- https://github.com/laravel-frontend-presets
- Show country flags: https://github.com/MohmmedAshraf/blade-flags
- Single file of Livewire: Volt | Laravel Livewire
- Print markdown: https://github.com/thephpleague/commonmark 
- Table:
    - https://github.com/Power-Components/livewire-powergrid
    - https://github.com/rappasoft/laravel-livewire-tables
    - https://github.com/yajra/laravel-datatables
- Form: https://github.com/rawilk/laravel-form-components
- Hotwire: https://github.com/hotwired-laravel/turbo-laravel
- Use React or Vue with Livewire: https://github.com/ijpatricio/mingle

## 3.1 Blade & Livewire

- Components:
    - https://github.com/robsontenorio/mary
    - https://github.com/mkocansey/bladewind
    - https://laravelcollective.com
    - https://github.com/cagilo/cagilo
    - https://github.com/kompo/kompo
    - https://github.com/wireui/wireui
    - https://github.com/tallstackui/tallstackui
- Livewire toast: https://github.com/masmerise/livewire-toaster
- Flash / Notification: https://github.com/mckenziearts/laravel-notify
- Dropzone: https://github.com/dasundev/livewire-dropzone

## 3.2 Inertia

- Components: https://github.com/protonemedia/laravel-splade

# 4 Các bước triển khai dự án Laravel

- Cài Laravel Breeze tạo auth cơ bản
    - https://bootcamp.laravel.com/inertia/creating-chirps
- Cài Material Icons
- Cài Laravel Authentication, Middleware
- Cài đa ngôn ngữ
- Tạo view components, layouts,...
- Cài search engine

# 5 Features

- Short URL: https://github.com/ash-jc-allen/short-url
- Log viewer: https://github.com/opcodesio/log-viewer
- Comments: https://github.com/usamamuneerchaudhary/commentify
- Roles and permissions: https://github.com/santigarcor/laratrust

## 5.1 Chatting

- https://github.com/musonza/chat
- https://github.com/munafio/chatify

## 5.2 File managers

- https://github.com/UniSharp/laravel-filemanager
- https://github.com/alexusmai/laravel-file-manager

# 6 Frameworks

- For interacting with AI/ML models: https://github.com/distantmagic/resonance

# 7 Libraries - Utilities

- https://spatie.be
- Filament: Admin dashboard preset
	- Starter: https://github.com/Log1x/filament-starter
	- Tricks: https://v2.filamentphp.com/tricks
- Laravel shopping cart: https://github.com/darryldecode/laravelshoppingcart
- Laravel page speed: https://github.com/renatomarinho/laravel-page-speed
- Tablar: Admin preset
- Stats: https://github.com/stefanzweifel/laravel-stats
- Rate limiter for Laravel: https://github.com/GrahamCampbell/Laravel-Throttle
- Print PDF: https://github.com/elibyy/tcpdf-laravel
- Slug generator: https://github.com/cviebrock/eloquent-sluggable
- No captcha for reCaptcha: https://github.com/anhskohbo/no-captcha
- High performance API serving: https://laravel.com/docs/10.x/octane
- Manage js imported for views: https://github.com/tonysm/importmap-laravel
- Track visiting of user: https://github.com/coderflexx/laravisit
- Automatic PHPDoc for IDE: https://github.com/barryvdh/laravel-ide-helper
- I/O blocking PHP: https://github.com/reactphp/reactphp
- Paypal: https://srmklive.github.io/laravel-paypal/docs.html
- Làm ứng dụng desktop: https://github.com/NativePHP/laravel
- Tổ chức dự án Laravel thành dạng module/package: https://github.com/nWidart/laravel-modules
- Micro-famework for console app: https://github.com/laravel-zero/laravel-zero
- Track Laravel jobs: https://github.com/mateusjunges/trackable-jobs-for-laravel
- Work with enum: https://github.com/emreyarligan/enum-concern
- Make tasks running as multiple roles: https://github.com/lorisleiva/laravel-actions
- Page based router: Laravel Folio - Laravel 10.x - The PHP Framework For Web Artisans
- Socket: https://github.com/laravel/echo
- Async and Parallel:
    - https://github.com/vuongxuongminh/laravel-async
    - https://github.com/spatie/async
- Tinker - Chạy lệnh PHP trong terminal/command line: https://github.com/laravel/tinker
- Share state from PHP to JS: https://github.com/rmunate/PHP2JS
- Elasticsearch driver for Laravel Scout: https://github.com/Jeroen-G/Explorer
- Laravel Stub - Create custom stub (ex: `example()`) in Laravel: https://github.com/binafy/laravel-stub
- Basset - an alternative way to load CSS & JS assets: https://github.com/Laravel-Backpack/basset
- Laravel Process Approval: https://github.com/ringlesoft/laravel-process-approval
- Phone number validate: https://github.com/Propaganistas/Laravel-Phone
- Query fitler for restful API: https://github.com/nvmcommunity/alchemist-restful-api
- Backup: https://github.com/spatie/laravel-backup
	- UI - Panel: https://github.com/pavel-mironchik/laravel-backup-panel
- Upgrade tools:
	- https://laravelshift.com/shifts
- An AI bot made for the command line that can read and understand any codebase from your Laravel app: https://github.com/joshembling/laragenie
- Snowflake ID: https://github.com/qh-8/laravel-snowflake
- Build blog: https://github.com/themsaid/wink
- Generate ERD diagrams: https://github.com/recca0120/laravel-erd
- Adjust context infomation: Use `Context` facade
- Run PHP code concurrently: https://github.com/spatie/fork
- converge - Advanced documentation management framework for Laravel artisans: https://github.com/convergephp/converge

## 7.1 Architecture

- Laravel Actions - Write business code straightforwardly: https://github.com/lorisleiva/laravel-actions
- Porto implementation for Laravel: https://github.com/apiato/apiato

## 7.2 Optimization

- enlightn - Scan and provide actionable recommendations on improving its performance, security & more: https://www.laravel-enlightn.com

## 7.3 Official libraries/packages

- Laravel Reverb - a first-party WebSocket server for Laravel applications: https://reverb.laravel.com

## 7.4 SEO

- https://github.com/artesaos/seotools
- https://github.com/eusonlito/laravel-Meta

## 7.5 Webhook

- https://github.com/spatie/laravel-webhook-server
- https://github.com/spatie/laravel-webhook-client

## 7.6 Code generator

- https://github.com/CrestApps/laravel-code-generator
- Blueprint: https://github.com/laravel-shift/blueprint

## 7.7 Starter / Template / Structure generator

- DDD: https://github.com/thejano/laravel-domain-driven-design
- DDD with Command bus: https://github.com/lucidarch/lucid

## 7.8 Automatic migrations

- https://github.com/kitloong/laravel-migrations-generator
- https://github.com/bastinald/laravel-automatic-migrations

## 7.9 Model

- Wallet - Ví: https://github.com/bavix/laravel-wallet
- Get human timestamps: https://github.com/chrisdicarlo/eloquent-human-timestamps
- Tạo chức năng “Lịch sử chỉnh sửa” cho model: https://github.com/VentureCraft/revisionable
- Lock instance of model: https://github.com/sfolador/laravel-locked
- Clone instance: https://github.com/BKWLD/cloner
- Delete casade: https://github.com/michaeldyrynda/laravel-cascade-soft-deletes
- Hierarchy: https://github.com/lazychaser/laravel-nestedset
- Fulltext: https://github.com/swisnl/laravel-fulltext
- Wallet: https://github.com/bavix/laravel-wallet
- A simple, drop-in drafts/revisions system for Laravel models: https://github.com/oddvalue/laravel-drafts
- Cross model searching: https://github.com/protonemedia/laravel-cross-eloquent-search
- Make model markable: https://github.com/maize-tech/laravel-markable
- Count visited: https://github.com/coderflexx/laravisit
- Prunable - Dọn dẹp bản ghi không cần thiết trong cơ sở dữ liệu:
    - https://medium.com/@antoine.lame/laravel-prunable-trait-periodically-remove-obsolete-models-b0662019ced2
    - https://www.youtube.com/watch?v=EhV8en0HIow
- Provides convenient methods for making token code, sending and verifying: https://github.com/mohammad-fouladgar/laravel-mobile-verification
- Tenants for Laravel: https://github.com/archtechx/tenancy

## 7.10 API Doc

- https://github.com/dedoc/scramble
- https://github.com/ovac/idoc

## 7.11 Cache

- https://github.com/spatie/laravel-responsecache

## 7.12 Lint

- Laracon Online Summer 2021: Next Level Type Checking In Laravel
- Laravel Pint - Laravel - The PHP Framework For Web Artisans
- https://github.com/nunomaduro/larastan
- https://github.com/vimeo/psalm

## 7.13 Query

- Recursive query: https://github.com/staudenmeir/laravel-adjacency-list
- Tăng tốc paginate với SQL deferred: https://github.com/hammerstonedev/fast-paginate

## 7.14 Generate mocks

- https://github.com/sebastianbergmann/phpunit
- https://github.com/vektra/mockery

## 7.15 DTO

- https://github.com/milwad-dev/laravel-validate
- https://github.com/WendellAdriel/laravel-validated-dto
- https://github.com/spatie/laravel-data

## 7.16 Mail

- User email vertification: https://github.com/jrean/laravel-user-verification
- Beautiful email template: https://github.com/Snowfire/Beautymail
- MailEclipse - Mail template editor: https://github.com/Qoraiche/laravel-mail-editor

## 7.17 Authentication

If you want a simple authentication, go with Fortify If you want a simple authentication with forms, go Breeze If you want SPAs and Mobile auths, go with Sanctum or Laravel Passport If you want a FULL auth with some features like 2FA, Teams, etc, go with Jetstream

- Authentication trong Laravel
- Laravel Fortify: https://laravel.com/docs/10.x/fortify
- Sanctum: Dùng khi xác thực token-based. Thường dùng cho SPA, app,...
- https://github.com/Laragear/TwoFactor
- Passport: Dùng OAuth2. Thường dùng cho web bình thường
- JWT: https://jwt-auth.readthedocs.io
- Implement API Token đơn giản: Laravel API Authentication với API Token | TechHay blog
- Phân quyền trong Laravel: https://www.youtube.com/watch?v=Eok6ySXD1u8
- Multiple authentication: https://www.youtube.com/watch?v=JkZcCxv1mlY

# 8 Kiến thức khác

- Shop bán source có sẵn cập nhật mới nhất: Botble - Laravel Outsourcing Team
- Dependency Injection
- Reflection: DI dựa trên cái này
- Lumen: Framework dựa trên Laravel, dùng cho microservices
- SPA cho Laravel: https://inertiajs.com
- Searching feature
    - Index trong MySQL
    - Full Text Search sử dụng MATCH, AGAINST
    - Một số search engine mạnh mẽ
    - Laravel Scout

# 9 Note

- Cấu hình supervisor chạy realtime hàng đợi: https://www.youtube.com/watch?v=Bj9drmpDeRE
- In ra raw SQL:

```php
dd(Str::replaceArray('?', $query->getBindings(), $query->toSql()));
```

- Laravel provides various methods for retrieving input data:

```php
$request->all();
$request->collect();
$request->input();
$request->query();
$request->string('name');
$request->boolean('archived');
$request->date('birthday');
$request->date('elapsed', '!H:i', 'Europe/Madrid');
$request->enum('status', Status::class);
```

- Một số cách sử dụng CommandBus: https://www.facebook.com/groups/167363136987053/?multi_permalinks=2207976906258989
- Guard:
    - Khi sử dụng Auth mà không khai báo guard name:
        - Trường hợp có 1 guard đang logged thì Auth sẽ nhận guard đó.
        - Trường hợp 2 guard đang logged thì Auth sẽ nhận default guard
        - Trường hợp không có Auth nào đang logged thì Auth sẽ nhận cả 2 guard, tức là `Auth::check()` sẽ true nếu 1 trong 2 guard logged.
        - Khi xây dựng Multi Authenticate, để chắc chắn nhất thì luôn phải khai báo guard muốn sử dụng `(Auth::guard($name)->user())`, tránh sử dụng `Auth::user()` nếu không hiểu rõ 3 trường hợp bên trên.

# 10 CMS

- Forum: https://github.com/devaslanphp/forumium
- Admin panel
    - MoonShine: https://github.com/moonshine-software/moonshine
    - Premium admin panel: https://nova.laravel.com
    - https://voyager.devdojo.com
    - https://orchid.software
    - https://github.com/thedevdojo/wave
    - Backpack: https://github.com/laravel-backpack/crud
        - Devtool for Backpack (hỗ trợ define module bằng web UI): DevTools :: Backpack for Laravel
    - Winter CMS (github.com)
    - https://github.com/open-admin-org/open-admin
    - Botble: Botble - Laravel CMS, CRUD generator, Modular & Theme system, Role permissions, Multilingual blog by botble (codecanyon.net)
    - Twill: https://github.com/area17/twill
- Movie
    - OPhim CMS 2022
- Project management: https://github.com/lavalite/cms
- Track expenses, payments & create professional invoices & estimates: https://github.com/crater-invoice/crater

# 11 Development environment

- Tương tự như Laragon nhưng mà là CLI: Laravel Valet - Laravel 10.x - The PHP Framework For Web Artisans - https://laravel.com/docs/10.x/valet

# 12 Tips and tricks

- Cache properties to avoid repeated queries: https://twitter.com/LaraconAU/status/1730806496978510259
- Josh Hanley - Building a maintainable Livewire application - Laracon AU 2023: https://www.youtube.com/watch?v=esD6o8HYV5Q
- Bendmark query:
```php
[$count, $duration] = Benchmark::value(fn () => User::count())
```
- Datetime format:
```php
$user->created_at->diffForHumans();

=> "17 hours ago"

$user->created_at->diffForHumans([
    'parts' => 2
]);

=> "17 hours 54 minutes ago"

$user->created_at->diffForHumans([
    'parts' => 3,
    'join' => ', ',
]);

=> "17 hours, 54 minutes, 50 seconds ago"

$user->created_at->diffForHumans([
    'parts' => 3,
    'join' => 'short',
    'short' => true,
]);

=> "17h, 54m, 50s ago"
```

## 12.1 Naming conventions

| Element                          | Convention                                 | Good                                 | Bad                                           |
| -------------------------------- | ------------------------------------------ | ------------------------------------ | --------------------------------------------- |
| Controller                       | Singular                                   | PostController                       | PostsController                               |
| Route                            | Plural                                     | posts/1                              | post/1                                        |
| Named Route                      | Snake case with dot                        | users.show-active                    | users.show-active, show-active-users          |
| Model                            | Singular                                   | Post                                 | POSTS                                         |
| hasOne or belongsTo Relationship | Singular                                   | comment                              | comments                                      |
| All other relationships          | Plural                                     | comments                             | comment                                       |
| Table                            | Plural                                     | comments                             | comment                                       |
| Pivot table                      | Singular model names in alphabetical order | post_user                            | user_post                                     |
| Table column                     | Snake case without model name              | author_name                          | AuthorName                                    |
| Model property                   | Snake case                                 | created_at                           | ScommentCreatedAt                             |
| Foreign key                      | Singular model name with _id suffix        | post_id                              | Postid, id_post                               |
| Primary key                      |                                            |                                      | custom_id                                     |
| Migration                        |                                            | 20211231120006_create_comments_table |                                               |
| Method                           | camelCase                                  | getComments                          | get comments                                  |
| Method in resource controller    | Table                                      | store                                | saveComment                                   |
| Method in test class             | camelCase                                  | testCreateComment                    | test_create_comment                           |
| Variable                         | camelCase                                  | commentsWithAuthor                   | Scomments_with_author                         |
| Collection                       | Descriptive, plural                        | activeUsers = User::active().get()   | Sactive, Sdata                                |
| Object                           | Descriptive, singular                      | activeUser = User::active().first()  | Susers, Sobj                                  |
| Config and Language files        | Snake case                                 | post_comments_enabled                | postCommentsEnabled; post_comments_enabled    |
| View                             | Snake case                                 | show_comment.blade.php               | showComment.blade.php, show-comment.blade.php |
| Config                           | Snake case                                 | post_comments.php                    | postComments.php, post-comments.php           |
| Contract (interface)             | Adjective or noun                          | Authenticatable                      | AuthenticationInterface, Authentication       |
| Trait                            | Adjective                                  | Notifiable                           | NotificationTrait                             |

# 13 Helpers

Facade Number cho phép bạn format linh hoạt số theo nhiều phương thức khác nhau. Bên cạnh format số, facade này còn hỗ trợ những việc như quy đổi các giá trị, format text, làm tròn %, ... rất tiện lợi. Bạn có thể tham khảo doc tại: [https://laravel.com/docs/11.x/helpers#numbers](https://laravel.com/docs/11.x/helpers?fbclid=IwZXh0bgNhZW0CMTAAAR0llXR6-np1e3aF-TH37FQbzak8MjqXtdZhxho8cMPmt5RSFjYtfXQ-pnI_aem_QcStZyeKOGQ1S2ukEqyoDA#numbers)

![[Pasted image 20241008054441.png]]

Bạn muốn benchmark nhanh chóng đoạn code của mình chạy tốn bao nhiêu thời gian, thì facade benchmark chính là dành cho bạn: [https://laravel.com/docs/11.x/helpers#benchmarking](https://laravel.com/docs/11.x/helpers?fbclid=IwZXh0bgNhZW0CMTAAAR14c8VsapQyTaCvxuOV4FnsRyP-WtuS5UJgvWghHNE3sh82JUlCLWTfA8w_aem_Sor-AdHjeJ8JblZE3JUtpQ#benchmarking)

![[Pasted image 20241008054447.png]]

`once()` là một helper thú vị khi bạn muốn một hàm chỉ chạy duy nhất một lần và cache lại kết quả: [https://laravel.com/docs/11.x/helpers#method-once](https://laravel.com/docs/11.x/helpers)

![[Pasted image 20241008054452.png]]

`report` cho phép bạn đẩy exception bất kỳ về Exception Handler. Điều này tiện ở chỗ bạn sẽ có nơi tập trung để xử lý exception như bắn lên sentry, bắn log, bắn telegram, slack, hoặc bỏ qua exception nào đó không muốn xử lý.

![[Pasted image 20241008054458.png]]

`rescue` chạy một phương thức, catch toàn bộ exception và cho phép bạn linh hoạt xử lý trong trường hợp có lỗi xảy ra. Exception được chuyển về exceptionHandler như report nên bạn cũng không lo bị mất mát dữ liệu.  
  
rescue sẽ khá tiện lợi khi bạn mong muốn đoạn code được thực thi mà không bị dừng khi có exception xảy ra và bớt đi những đoạn try-catch.

![[Pasted image 20241008054704.png]]