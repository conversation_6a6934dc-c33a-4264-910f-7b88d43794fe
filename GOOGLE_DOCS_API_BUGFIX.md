# Google Docs API Bug Fix - fontFamily Issue

## Vấn đề gặp phải

Khi sync Obsidian notes có code blocks sang Google Docs, gặp lỗi:

```
HttpError 400: Invalid JSON payload received. 
Unknown name "fontFamily" at 'requests[2].update_text_style.text_style': Cannot find field.
```

## Nguyên nhân

Google Docs API đã thay đổi format cho font family:
- **<PERSON><PERSON> (không hoạt động):** `"fontFamily": "Courier New"`
- **Mới (đúng):** `"weightedFontFamily": {"fontFamily": "Courier New"}`

## Giải pháp

### 1. Sửa code formatting cho code blocks

**Trước khi sửa:**
```python
'textStyle': {
    'fontFamily': 'Courier New',  # ❌ Sai format
    'backgroundColor': {...}
},
'fields': 'fontFamily,backgroundColor'  # ❌ Sai field name
```

**<PERSON>u khi sửa:**
```python
'textStyle': {
    'weightedFontFamily': {        # ✅ Đúng format
        'fontFamily': 'Courier New'
    },
    'backgroundColor': {...}
},
'fields': 'weightedFontFamily,backgroundColor'  # ✅ Đúng field name
```

### 2. Thêm request validation

Tạo method `validate_requests()` để:
- Tự động chuyển đổi `fontFamily` → `weightedFontFamily`
- Cập nhật `fields` string tương ứng
- Skip requests không hợp lệ
- Log chi tiết để debug

```python
def validate_requests(self, requests: List[Dict]) -> List[Dict]:
    """Validate and clean requests before sending to Google Docs API"""
    valid_requests = []
    
    for i, request in enumerate(requests):
        try:
            # Check for common issues
            if 'updateTextStyle' in request:
                text_style = request['updateTextStyle'].get('textStyle', {})
                
                # Ensure fontFamily is in correct format
                if 'fontFamily' in text_style:
                    font_family = text_style.pop('fontFamily')
                    text_style['weightedFontFamily'] = {'fontFamily': font_family}
                    
                    # Update fields
                    fields = request['updateTextStyle'].get('fields', '')
                    fields = fields.replace('fontFamily', 'weightedFontFamily')
                    request['updateTextStyle']['fields'] = fields
            
            valid_requests.append(request)
            
        except Exception as e:
            logger.warning(f"Skipping invalid request {i}: {e}")
            continue
    
    return valid_requests
```

### 3. Cải thiện error handling

**Enhanced logging trong `create_google_doc()`:**
```python
# Apply content formatting if there are requests
if content_requests:
    logger.info(f"Applying {len(content_requests)} formatting requests...")
    
    # Validate requests before sending
    validated_requests = self.validate_requests(content_requests)
    
    if validated_requests:
        try:
            self.docs_service.documents().batchUpdate(
                documentId=doc_id,
                body={'requests': validated_requests}
            ).execute()
            logger.info(f"Successfully applied formatting to document: {title}")
        except HttpError as format_error:
            logger.error(f"Error applying formatting to {title}: {format_error}")
            logger.error(f"Problematic requests: {validated_requests}")
            # Document is created but without formatting
            return doc_id
    else:
        logger.warning(f"No valid requests to apply for document: {title}")
```

## Testing

### Test case mới

Thêm test `test_request_validation()` để verify:
- Conversion từ `fontFamily` → `weightedFontFamily`
- Fields string được cập nhật đúng
- Requests không hợp lệ bị skip

```python
def test_request_validation():
    """Test request validation functionality"""
    # Test requests with old fontFamily format
    test_requests = [
        {
            'updateTextStyle': {
                'textStyle': {
                    'fontFamily': 'Courier New',  # Old format
                },
                'fields': 'fontFamily,backgroundColor'  # Old field
            }
        }
    ]
    
    # Validate requests
    validated = sync_tool.validate_requests(test_requests)
    
    # Check if fontFamily was converted to weightedFontFamily
    for request in validated:
        if 'updateTextStyle' in request:
            text_style = request['updateTextStyle']['textStyle']
            assert 'weightedFontFamily' in text_style  # ✅ New format
            assert 'fontFamily' not in text_style      # ❌ Old format removed
```

## Kết quả

### Trước khi fix:
- ❌ Sync fails với HttpError 400
- ❌ Code blocks không được format
- ❌ Documents không được tạo

### Sau khi fix:
- ✅ Sync thành công
- ✅ Code blocks có monospace font và background
- ✅ Documents được tạo với đầy đủ formatting
- ✅ Automatic validation và error recovery

## Tác động

1. **Backward compatibility:** Module tự động handle cả old và new format
2. **Error resilience:** Document vẫn được tạo ngay cả khi có formatting errors
3. **Better debugging:** Chi tiết logging cho troubleshooting
4. **Future-proof:** Validation framework có thể handle các API changes khác

## Files đã thay đổi

- `obsidian_to_google_sync.py`:
  - Fixed `updateTextStyle` format
  - Added `validate_requests()` method
  - Enhanced error handling in `create_google_doc()`

- `test_google_sync.py`:
  - Added `test_request_validation()` test case

- `GOOGLE_SYNC_README.md`:
  - Added troubleshooting section cho Google Docs API errors

- `GOOGLE_DOCS_API_BUGFIX.md`:
  - This documentation file

## Lesson learned

Google APIs có thể thay đổi format mà không có deprecation warning rõ ràng. Cần:
1. **Validation layer** cho tất cả API requests
2. **Comprehensive error handling** với fallback options
3. **Detailed logging** để debug issues nhanh chóng
4. **Regular testing** với real API calls

Điều này đảm bảo module robust và có thể handle API changes trong tương lai.
