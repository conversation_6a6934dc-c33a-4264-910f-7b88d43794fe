#!/usr/bin/env python3
"""
Obsidian to OneNote Sync Script
Synchronizes notes and media from Obsidian vault to Microsoft OneNote
"""

import os
import re
import json
import base64
import mimetypes
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import requests
from datetime import datetime
import markdown
from bs4 import BeautifulSoup
import argparse
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sync.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ObsidianToOneNoteSync:
    def __init__(self, obsidian_path: str, client_id: str, client_secret: str):
        """
        Initialize the sync tool
        
        Args:
            obsidian_path: Path to Obsidian vault
            client_id: Microsoft Graph API client ID
            client_secret: Microsoft Graph API client secret
        """
        self.obsidian_path = Path(obsidian_path)
        self.client_id = client_id
        self.client_secret = client_secret
        self.access_token = None
        self.notebooks = {}
        self.sections = {}
        
        # OneNote API endpoints
        self.graph_url = "https://graph.microsoft.com/v1.0"
        self.auth_url = "https://login.microsoftonline.com/common/oauth2/v2.0"
        
        # Supported media types
        self.supported_media = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg', '.pdf', '.mp4', '.mp3', '.wav'}
        
    def authenticate(self) -> bool:
        """Authenticate with Microsoft Graph API"""
        try:
            # For device code flow authentication
            device_code_url = f"{self.auth_url}/devicecode"
            token_url = f"{self.auth_url}/token"
            
            # Request device code
            device_data = {
                'client_id': self.client_id,
                'scope': 'https://graph.microsoft.com/Notes.ReadWrite offline_access'
            }
            
            response = requests.post(device_code_url, data=device_data)
            device_info = response.json()
            
            print(f"Please visit: {device_info['verification_uri']}")
            print(f"Enter code: {device_info['user_code']}")
            input("Press Enter after completing authentication...")
            
            # Exchange device code for access token
            token_data = {
                'client_id': self.client_id,
                'grant_type': 'urn:ietf:params:oauth:grant-type:device_code',
                'device_code': device_info['device_code']
            }
            
            token_response = requests.post(token_url, data=token_data)
            token_info = token_response.json()
            
            if 'access_token' in token_info:
                self.access_token = token_info['access_token']
                logger.info("Authentication successful")
                return True
            else:
                logger.error(f"Authentication failed: {token_info}")
                return False
                
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return False
    
    def get_headers(self) -> Dict[str, str]:
        """Get headers for API requests"""
        return {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }
    
    def get_notebooks(self) -> Dict[str, str]:
        """Get all OneNote notebooks"""
        try:
            url = f"{self.graph_url}/me/onenote/notebooks"
            response = requests.get(url, headers=self.get_headers())
            
            if response.status_code == 200:
                notebooks = response.json()['value']
                self.notebooks = {nb['displayName']: nb['id'] for nb in notebooks}
                logger.info(f"Found {len(self.notebooks)} notebooks")
                return self.notebooks
            else:
                logger.error(f"Failed to get notebooks: {response.text}")
                return {}
                
        except Exception as e:
            logger.error(f"Error getting notebooks: {e}")
            return {}
    
    def create_notebook(self, name: str) -> Optional[str]:
        """Create a new notebook"""
        try:
            url = f"{self.graph_url}/me/onenote/notebooks"
            data = {'displayName': name}
            
            response = requests.post(url, headers=self.get_headers(), json=data)
            
            if response.status_code == 201:
                notebook_id = response.json()['id']
                self.notebooks[name] = notebook_id
                logger.info(f"Created notebook: {name}")
                return notebook_id
            else:
                logger.error(f"Failed to create notebook: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating notebook: {e}")
            return None
    
    def get_sections(self, notebook_id: str) -> Dict[str, str]:
        """Get sections in a notebook"""
        try:
            url = f"{self.graph_url}/me/onenote/notebooks/{notebook_id}/sections"
            response = requests.get(url, headers=self.get_headers())
            
            if response.status_code == 200:
                sections = response.json()['value']
                return {sec['displayName']: sec['id'] for sec in sections}
            else:
                logger.error(f"Failed to get sections: {response.text}")
                return {}
                
        except Exception as e:
            logger.error(f"Error getting sections: {e}")
            return {}
    
    def create_section(self, notebook_id: str, name: str) -> Optional[str]:
        """Create a new section"""
        try:
            url = f"{self.graph_url}/me/onenote/notebooks/{notebook_id}/sections"
            data = {'displayName': name}
            
            response = requests.post(url, headers=self.get_headers(), json=data)
            
            if response.status_code == 201:
                section_id = response.json()['id']
                logger.info(f"Created section: {name}")
                return section_id
            else:
                logger.error(f"Failed to create section: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating section: {e}")
            return None
    
    def convert_markdown_to_html(self, markdown_content: str, note_path: Path) -> str:
        """Convert Obsidian markdown to HTML suitable for OneNote"""
        try:
            # Convert basic markdown to HTML
            html = markdown.markdown(markdown_content, extensions=['tables', 'fenced_code'])
            
            # Parse with BeautifulSoup for manipulation
            soup = BeautifulSoup(html, 'html.parser')
            
            # Handle Obsidian-specific syntax
            html_content = str(soup)
            
            # Convert Obsidian links [[link]] to regular links
            obsidian_link_pattern = r'\[\[([^\]]+)\]\]'
            html_content = re.sub(obsidian_link_pattern, r'<a href="#\1">\1</a>', html_content)
            
            # Handle embedded images ![[image.png]]
            embedded_image_pattern = r'!\[\[([^\]]+)\]\]'
            def replace_embedded_image(match):
                image_name = match.group(1)
                image_path = self.find_media_file(note_path.parent, image_name)
                if image_path and image_path.exists():
                    # Convert image to base64 for embedding
                    return self.embed_image_html(image_path)
                return f'<p>Image not found: {image_name}</p>'
            
            html_content = re.sub(embedded_image_pattern, replace_embedded_image, html_content)
            
            return html_content
            
        except Exception as e:
            logger.error(f"Error converting markdown: {e}")
            return f"<p>Error converting content: {str(e)}</p>"
    
    def find_media_file(self, base_path: Path, filename: str) -> Optional[Path]:
        """Find media file in Obsidian vault"""
        # Common media directories in Obsidian
        search_paths = [
            base_path / filename,
            base_path / "attachments" / filename,
            base_path / "assets" / filename,
            base_path / "media" / filename,
            self.obsidian_path / "attachments" / filename,
            self.obsidian_path / "assets" / filename,
            self.obsidian_path / "media" / filename
        ]
        
        for path in search_paths:
            if path.exists():
                return path
        
        # Search recursively if not found
        for path in self.obsidian_path.rglob(filename):
            return path
        
        return None
    
    def embed_image_html(self, image_path: Path) -> str:
        """Convert image to base64 embedded HTML"""
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            mime_type = mimetypes.guess_type(str(image_path))[0] or 'image/png'
            base64_data = base64.b64encode(image_data).decode('utf-8')
            
            return f'<img src="data:{mime_type};base64,{base64_data}" alt="{image_path.name}" />'
            
        except Exception as e:
            logger.error(f"Error embedding image {image_path}: {e}")
            return f'<p>Error loading image: {image_path.name}</p>'
    
    def create_page(self, section_id: str, title: str, content: str) -> bool:
        """Create a page in OneNote"""
        try:
            url = f"{self.graph_url}/me/onenote/sections/{section_id}/pages"
            
            # Create HTML content for OneNote
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>{title}</title>
                <meta name="created" content="{datetime.now().isoformat()}" />
            </head>
            <body>
                <div>
                    {content}
                </div>
            </body>
            </html>
            """
            
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'text/html'
            }
            
            response = requests.post(url, headers=headers, data=html_content.encode('utf-8'))
            
            if response.status_code == 201:
                logger.info(f"Created page: {title}")
                return True
            else:
                logger.error(f"Failed to create page {title}: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error creating page {title}: {e}")
            return False

    def get_obsidian_notes(self) -> List[Path]:
        """Get all markdown files from Obsidian vault"""
        markdown_files = []

        if not self.obsidian_path.exists():
            logger.error(f"Obsidian path does not exist: {self.obsidian_path}")
            return []

        for md_file in self.obsidian_path.rglob("*.md"):
            # Skip template files and system files
            if not any(part.startswith('.') for part in md_file.parts):
                markdown_files.append(md_file)

        logger.info(f"Found {len(markdown_files)} markdown files")
        return markdown_files

    def organize_notes_by_folder(self, notes: List[Path]) -> Dict[str, List[Path]]:
        """Organize notes by their folder structure"""
        organized = {}

        for note in notes:
            # Get relative path from obsidian root
            rel_path = note.relative_to(self.obsidian_path)
            folder = str(rel_path.parent) if rel_path.parent != Path('.') else 'Root'

            if folder not in organized:
                organized[folder] = []
            organized[folder].append(note)

        return organized

    def sync_notes(self, notebook_name: str = "Obsidian Sync") -> bool:
        """Main sync function"""
        try:
            logger.info("Starting Obsidian to OneNote sync...")

            # Authenticate
            if not self.authenticate():
                return False

            # Get or create notebook
            self.get_notebooks()
            if notebook_name not in self.notebooks:
                notebook_id = self.create_notebook(notebook_name)
                if not notebook_id:
                    return False
            else:
                notebook_id = self.notebooks[notebook_name]

            # Get notes from Obsidian
            notes = self.get_obsidian_notes()
            if not notes:
                logger.warning("No notes found in Obsidian vault")
                return True

            # Organize notes by folder
            organized_notes = self.organize_notes_by_folder(notes)

            # Sync each folder as a section
            for folder_name, folder_notes in organized_notes.items():
                logger.info(f"Syncing folder: {folder_name} ({len(folder_notes)} notes)")

                # Get or create section
                sections = self.get_sections(notebook_id)
                if folder_name not in sections:
                    section_id = self.create_section(notebook_id, folder_name)
                    if not section_id:
                        logger.error(f"Failed to create section: {folder_name}")
                        continue
                else:
                    section_id = sections[folder_name]

                # Sync notes in this folder
                for note_path in folder_notes:
                    self.sync_single_note(note_path, section_id)

            logger.info("Sync completed successfully!")
            return True

        except Exception as e:
            logger.error(f"Sync failed: {e}")
            return False

    def sync_single_note(self, note_path: Path, section_id: str) -> bool:
        """Sync a single note to OneNote"""
        try:
            logger.info(f"Syncing note: {note_path.name}")

            # Read markdown content
            with open(note_path, 'r', encoding='utf-8') as f:
                markdown_content = f.read()

            # Convert to HTML
            html_content = self.convert_markdown_to_html(markdown_content, note_path)

            # Use filename (without extension) as title
            title = note_path.stem

            # Create page in OneNote
            return self.create_page(section_id, title, html_content)

        except Exception as e:
            logger.error(f"Error syncing note {note_path}: {e}")
            return False

    def create_config_file(self, config_path: str = "sync_config.json"):
        """Create a configuration file template"""
        config = {
            "obsidian_path": "./obsidian",
            "client_id": "YOUR_MICROSOFT_APP_CLIENT_ID",
            "client_secret": "YOUR_MICROSOFT_APP_CLIENT_SECRET",
            "notebook_name": "Obsidian Sync",
            "excluded_folders": [".obsidian", ".trash"],
            "excluded_files": ["*.tmp", "*.bak"]
        }

        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2)

        logger.info(f"Configuration file created: {config_path}")
        print(f"Please edit {config_path} with your Microsoft App credentials")


def main():
    parser = argparse.ArgumentParser(description="Sync Obsidian notes to OneNote")
    parser.add_argument("--obsidian-path", default="./obsidian",
                       help="Path to Obsidian vault (default: ./obsidian)")
    parser.add_argument("--config", default="sync_config.json",
                       help="Configuration file path (default: sync_config.json)")
    parser.add_argument("--create-config", action="store_true",
                       help="Create a configuration file template")
    parser.add_argument("--notebook-name", default="Obsidian Sync",
                       help="OneNote notebook name (default: Obsidian Sync)")

    args = parser.parse_args()

    if args.create_config:
        sync_tool = ObsidianToOneNoteSync("", "", "")
        sync_tool.create_config_file(args.config)
        return

    # Load configuration
    if os.path.exists(args.config):
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)

        obsidian_path = config.get("obsidian_path", args.obsidian_path)
        client_id = config.get("client_id", "")
        client_secret = config.get("client_secret", "")
        notebook_name = config.get("notebook_name", args.notebook_name)
    else:
        print(f"Configuration file not found: {args.config}")
        print("Run with --create-config to create a template")
        return

    if not client_id or not client_secret:
        print("Please configure your Microsoft App credentials in the config file")
        return

    # Initialize and run sync
    sync_tool = ObsidianToOneNoteSync(obsidian_path, client_id, client_secret)
    success = sync_tool.sync_notes(notebook_name)

    if success:
        print("Sync completed successfully!")
    else:
        print("Sync failed. Check the log for details.")


if __name__ == "__main__":
    main()
