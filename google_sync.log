2025-07-23 15:39:32,872 - INFO - Found 2 markdown files in Obsidian vault
2025-07-23 15:39:32,886 - INFO - Configuration file created: /var/folders/gg/n96mjvnn331by57zqfwkj4jc0000gn/T/tmp5vpf_sk5.json
2025-07-23 15:39:45,376 - INFO - Configuration file created: google_sync_config.json
2025-07-23 15:47:40,923 - INFO - Configuration file created: google_sync_config.json
2025-07-23 15:50:12,873 - INFO - Starting Obsidian to Google Docs/Drive sync...
2025-07-23 15:52:18,190 - INFO - Starting Obsidian to Google Docs/Drive sync...
2025-07-23 15:52:32,144 - INFO - "GET /?state=cKEolT7JLEZIdlPK8vibfyRkM3XOK0&code=4/0AVMBsJhER7KSTACBL1CUe38BNK3xF8nkRct1_-BdwGT6kty-HqseNNSXuOOlMzd5IlWTEQ&scope=https://www.googleapis.com/auth/drive%20https://www.googleapis.com/auth/documents HTTP/1.1" 200 65
2025-07-23 15:52:32,330 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-07-23 15:52:32,332 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-07-23 15:52:32,334 - INFO - Google authentication successful
2025-07-23 15:52:35,585 - INFO - Created folder: Obsidian Sync (ID: 1t6AI19Oj4rJhWFGPekxYkyvKzdCzZvze)
2025-07-23 15:52:37,632 - INFO - Created folder: Media (ID: 14YGgZka9M8qAgLNqd9nDkB5BffMK8Cqp)
2025-07-23 15:52:37,637 - INFO - Found 218 markdown files in Obsidian vault
2025-07-23 15:52:37,639 - INFO - Syncing folder: Root (214 notes)
2025-07-23 15:52:37,639 - INFO - Syncing note: Kafka.md
2025-07-23 15:52:43,259 - INFO - Created Google Doc: Kafka (ID: 1BUCuDeVjM7UaZy-QMvP5IuClsVM9IKSM_NImu5RecGU)
2025-07-23 15:52:43,260 - INFO - Syncing note: Bài toán liệt kê.md
2025-07-23 15:52:49,262 - INFO - Created Google Doc: Bài toán liệt kê (ID: 11iurgy_UH2NNcZOIt4ARafZz1LUV2avktQAvvCae1Bk)
2025-07-23 15:52:49,263 - INFO - Syncing note: Vue - Nuxt.md
2025-07-23 15:52:55,224 - INFO - Created Google Doc: Vue - Nuxt (ID: 1GOzUuA56-w9j2BJFUANpW4EDMMxRz6MpEOqArFT2iUw)
2025-07-23 15:52:55,225 - INFO - Syncing note: Kudofoto.md
2025-07-23 15:52:59,762 - ERROR - Error creating Google Doc Kudofoto: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1otA17gTzb1Fa360-LWTuFwv2iwHxzC1zykw2jjQWKIc:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[2].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[2].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[2].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 15:52:59,763 - INFO - Syncing note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:52:59,828 - INFO - Found embedded image: Pasted image 20241012194316.png
2025-07-23 15:52:59,829 - INFO - Found embedded image: Pasted image 20240425163824.png
2025-07-23 15:52:59,829 - INFO - Found embedded image: Pasted image 20240425163928.png
2025-07-23 15:52:59,829 - INFO - Found embedded image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 15:52:59,829 - INFO - Found embedded image: Pasted image 20240927232457.png
2025-07-23 15:52:59,829 - INFO - Found embedded image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 15:52:59,829 - INFO - Found embedded image: Pasted image 20240903230303.png
2025-07-23 15:52:59,829 - INFO - Found embedded image: Pasted image 20240903230309.png
2025-07-23 15:52:59,829 - INFO - Found embedded image: Pasted image 20240903223244.png
2025-07-23 15:52:59,829 - INFO - Found embedded image: Pasted image 20240904085651.png
2025-07-23 15:52:59,829 - INFO - Found embedded image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 15:52:59,829 - INFO - Found embedded image: Pasted image 20241012194316.png
2025-07-23 15:52:59,829 - INFO - Found embedded image: Untitled 3.png
2025-07-23 15:52:59,829 - INFO - Found embedded image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 15:52:59,829 - INFO - Found embedded image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 15:52:59,829 - INFO - Found embedded image: Pasted image 20240927232457.png
2025-07-23 15:52:59,830 - INFO - Found embedded image: Pasted image 20240903223244.png
2025-07-23 15:52:59,830 - INFO - Found embedded image: Pasted image 20240903230303.png
2025-07-23 15:52:59,830 - INFO - Found embedded image: Pasted image 20240903230309.png
2025-07-23 15:52:59,830 - INFO - Found embedded image: Pasted image 20240425163824.png
2025-07-23 15:52:59,830 - INFO - Found embedded image: Pasted image 20240425163928.png
2025-07-23 15:52:59,830 - INFO - Found embedded image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 15:52:59,830 - INFO - Found embedded image: Pasted image 20240904085651.png
2025-07-23 15:53:06,909 - INFO - Created Google Doc: Solutions & System Designs & Design Patterns (ID: 1D-8vLLWyxzEHWDN33oRqDdanvydRfFJdQoNsHqDCPTM)
2025-07-23 15:53:10,508 - INFO - Uploaded media file: Pasted image 20241012194316.png (ID: 17yetG7IRZGBH5IQPzfW9yijZ5Wo0JFQZ)
2025-07-23 15:53:10,508 - INFO - Uploaded media: Pasted image 20241012194316.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:53:13,749 - INFO - Uploaded media file: Pasted image 20240425163824.png (ID: 1epWcUk3Bg4m8hG-J_jb1gM6BAzfjKiyb)
2025-07-23 15:53:13,749 - INFO - Uploaded media: Pasted image 20240425163824.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:53:16,861 - INFO - Uploaded media file: Pasted image 20240425163928.png (ID: 1a7zCIz1-3wMHeWsI7jCkO77p6PKV8LnX)
2025-07-23 15:53:16,862 - INFO - Uploaded media: Pasted image 20240425163928.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:53:20,149 - INFO - Uploaded media file: telegram-cloud-photo-size-5-6311899726957623527-y.jpg (ID: 1gJiPRw2gQWTm1zyMuEmq_tZpPOPnCzwM)
2025-07-23 15:53:20,150 - INFO - Uploaded media: telegram-cloud-photo-size-5-6311899726957623527-y.jpg for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:53:22,669 - INFO - Uploaded media file: Pasted image 20240927232457.png (ID: 1PyG0P4CRHGMzkXfj4QsBTwcCFVXLTI9O)
2025-07-23 15:53:22,669 - INFO - Uploaded media: Pasted image 20240927232457.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:53:26,140 - INFO - Uploaded media file: bddf3546-c720-4313-9046-36d8c4a97019.png (ID: 1ilRrncy10H0s0JacMO5mWPFFVg5lv4Ut)
2025-07-23 15:53:26,140 - INFO - Uploaded media: bddf3546-c720-4313-9046-36d8c4a97019.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:53:29,898 - INFO - Uploaded media file: Pasted image 20240903230303.png (ID: 1xKyZ_H1ETwvC102De0PfT_MRZsIAioEt)
2025-07-23 15:53:29,898 - INFO - Uploaded media: Pasted image 20240903230303.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:53:33,120 - INFO - Uploaded media file: Pasted image 20240903230309.png (ID: 1H5mj7saNVfl--rLQzFNFfOUhC4J1Q42c)
2025-07-23 15:53:33,120 - INFO - Uploaded media: Pasted image 20240903230309.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:53:35,886 - INFO - Uploaded media file: Pasted image 20240903223244.png (ID: 1NzG8V7sC8ruaHDBTEbh9X6VBd1U1t1im)
2025-07-23 15:53:35,886 - INFO - Uploaded media: Pasted image 20240903223244.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:53:38,705 - INFO - Uploaded media file: Pasted image 20240904085651.png (ID: 1PHJ3gjnzqcolY-uNF0mJPGvzUU-4-yQE)
2025-07-23 15:53:38,706 - INFO - Uploaded media: Pasted image 20240904085651.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:53:41,908 - INFO - Uploaded media file: 390dd032e50c3364eec22e71a19b2113_MD5.jpg (ID: 1LpgTJ27LOhdHwsODGKwpTihD-8M6g2Ly)
2025-07-23 15:53:41,909 - INFO - Uploaded media: 390dd032e50c3364eec22e71a19b2113_MD5.jpg for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:53:45,086 - INFO - Uploaded media file: Pasted image 20241012194316.png (ID: 1aAAl1w0y0aMmgRiE-RJs5n-qMdigfsfp)
2025-07-23 15:53:45,086 - INFO - Uploaded media: Pasted image 20241012194316.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:53:48,626 - INFO - Uploaded media file: Untitled 3.png (ID: 1_KDZR9a9vcDQQ4waBmYGrUgLf67xBocg)
2025-07-23 15:53:48,627 - INFO - Uploaded media: Untitled 3.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:53:51,820 - INFO - Uploaded media file: 390dd032e50c3364eec22e71a19b2113_MD5.jpg (ID: 1yRn4ZGTZoy6cHQ3l90KEuZ-sLM58ZTZq)
2025-07-23 15:53:51,820 - INFO - Uploaded media: 390dd032e50c3364eec22e71a19b2113_MD5.jpg for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:53:55,176 - INFO - Uploaded media file: bddf3546-c720-4313-9046-36d8c4a97019.png (ID: 1uoWa41TYCP_g_u_hA5f62wUhsS0ndNZn)
2025-07-23 15:53:55,177 - INFO - Uploaded media: bddf3546-c720-4313-9046-36d8c4a97019.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:53:57,692 - INFO - Uploaded media file: Pasted image 20240927232457.png (ID: 1tG6vGyefCXV3M3ua3YzEAisOGeishSCD)
2025-07-23 15:53:57,692 - INFO - Uploaded media: Pasted image 20240927232457.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:54:00,334 - INFO - Uploaded media file: Pasted image 20240903223244.png (ID: 1lH6pDeReqs6sKun0H-4BIPNZZ4jrKFWe)
2025-07-23 15:54:00,334 - INFO - Uploaded media: Pasted image 20240903223244.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:54:03,416 - INFO - Uploaded media file: Pasted image 20240903230303.png (ID: 1SKfG0tpV8qltjp9zPGMROJRvvf3ew56q)
2025-07-23 15:54:03,417 - INFO - Uploaded media: Pasted image 20240903230303.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:54:06,992 - INFO - Uploaded media file: Pasted image 20240903230309.png (ID: 1ZY264KMePRnUd3ZJyaLzXtwhMLtlpg0v)
2025-07-23 15:54:06,993 - INFO - Uploaded media: Pasted image 20240903230309.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:54:10,406 - INFO - Uploaded media file: Pasted image 20240425163824.png (ID: 1EeON46PNEGEdlK_ZkTnca7WkFYofrGEO)
2025-07-23 15:54:10,407 - INFO - Uploaded media: Pasted image 20240425163824.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:54:13,068 - INFO - Uploaded media file: Pasted image 20240425163928.png (ID: 1nrxjGddHNK3VtWGj39vqlYyh2cZn1k4D)
2025-07-23 15:54:13,068 - INFO - Uploaded media: Pasted image 20240425163928.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:54:15,806 - INFO - Uploaded media file: telegram-cloud-photo-size-5-6311899726957623527-y.jpg (ID: 1YiC_xV6Wp_vmhxRMOthZaoZeq90u1Sag)
2025-07-23 15:54:15,807 - INFO - Uploaded media: telegram-cloud-photo-size-5-6311899726957623527-y.jpg for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:54:18,572 - INFO - Uploaded media file: Pasted image 20240904085651.png (ID: 11c5z7rHJh3OBdji23U0fl8L-uzz2JObi)
2025-07-23 15:54:18,572 - INFO - Uploaded media: Pasted image 20240904085651.png for note: Solutions & System Designs & Design Patterns.md
2025-07-23 15:54:18,573 - INFO - Syncing note: Domain knowledge.md
2025-07-23 15:54:22,115 - INFO - Created Google Doc: Domain knowledge (ID: 12Oqhu96fqNPr_v6LBA8AMdKhmKEDNqI9vEEoIw0SvdA)
2025-07-23 15:54:22,116 - INFO - Syncing note: VPN - Proxy - Firewall.md
2025-07-23 15:54:25,520 - INFO - Created Google Doc: VPN - Proxy - Firewall (ID: 1sHuMZOogR6kuiNG9T-bDdAw0xS4EgPTLwb_lY1_8N2o)
2025-07-23 15:54:25,520 - INFO - Syncing note: Cách làm sạch và bảo quản boots.md
2025-07-23 15:54:25,526 - INFO - Found embedded image: Untitled 16.png
2025-07-23 15:54:25,527 - INFO - Found embedded image: Untitled 1 9.png
2025-07-23 15:54:25,527 - INFO - Found embedded image: Untitled 2 6.png
2025-07-23 15:54:28,702 - INFO - Created Google Doc: Cách làm sạch và bảo quản boots (ID: 1x4xOWExpobgNMTIivrBqIO1zQbJAObXzn7t87PzBxJg)
2025-07-23 15:54:31,485 - INFO - Uploaded media file: Untitled 16.png (ID: 13te2V-Qz2AjscIJrkefeOfzcadVcc0YB)
2025-07-23 15:54:31,486 - INFO - Uploaded media: Untitled 16.png for note: Cách làm sạch và bảo quản boots.md
2025-07-23 15:54:34,496 - INFO - Uploaded media file: Untitled 1 9.png (ID: 15UmxPPcuPXH3RQw5pEXgy_0ZE82UvrT6)
2025-07-23 15:54:34,496 - INFO - Uploaded media: Untitled 1 9.png for note: Cách làm sạch và bảo quản boots.md
2025-07-23 15:54:37,176 - INFO - Uploaded media file: Untitled 2 6.png (ID: 1P9k9A6l0aCUgNUHeQQFrjlKivVHCxq9_)
2025-07-23 15:54:37,177 - INFO - Uploaded media: Untitled 2 6.png for note: Cách làm sạch và bảo quản boots.md
2025-07-23 15:54:37,177 - INFO - Syncing note: English.md
2025-07-23 15:54:37,182 - INFO - Found embedded image: 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png
2025-07-23 15:54:41,160 - INFO - Created Google Doc: English (ID: 1auiYHIDmlPOWEG06Y9Ho6gIbx6GcSfY8nVuUkUeqXBo)
2025-07-23 16:00:15,494 - INFO - Found 2 markdown files in Obsidian vault
2025-07-23 16:00:15,510 - INFO - Configuration file created: /var/folders/gg/n96mjvnn331by57zqfwkj4jc0000gn/T/tmpew_b0xnt.json
2025-07-23 16:01:17,870 - INFO - Found 2 markdown files in Obsidian vault
2025-07-23 16:01:17,912 - INFO - Configuration file created: /var/folders/gg/n96mjvnn331by57zqfwkj4jc0000gn/T/tmp9y85dg8i.json
2025-07-23 16:09:37,756 - INFO - Starting Obsidian to Google Docs/Drive sync...
2025-07-23 16:09:37,758 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-07-23 16:09:37,760 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-07-23 16:09:37,762 - INFO - Google authentication successful
2025-07-23 16:09:38,564 - INFO - Found existing folder: Obsidian Sync (ID: 1t6AI19Oj4rJhWFGPekxYkyvKzdCzZvze)
2025-07-23 16:09:38,996 - INFO - Found existing folder: Media (ID: 14YGgZka9M8qAgLNqd9nDkB5BffMK8Cqp)
2025-07-23 16:09:39,001 - INFO - Found 218 markdown files in Obsidian vault
2025-07-23 16:09:39,002 - INFO - Syncing folder: Root (214 notes)
2025-07-23 16:09:39,002 - INFO - Syncing note: Kafka.md
2025-07-23 16:09:43,084 - INFO - Created Google Doc: Kafka (ID: 1jlXD0Popu9BskX3wGnka3k8OhbdU_2qFUKJPJN62Z_0)
2025-07-23 16:09:43,084 - INFO - Syncing note: Bài toán liệt kê.md
2025-07-23 16:09:46,828 - INFO - Created Google Doc: Bài toán liệt kê (ID: 1h0pJe2O87C21NcKYTtbXNG8YeqCVUpJQb2LjfXQrZw4)
2025-07-23 16:09:46,828 - INFO - Syncing note: Vue - Nuxt.md
2025-07-23 16:09:52,859 - INFO - Created Google Doc: Vue - Nuxt (ID: 1A-cgkX31JEnrow3E7v_gM69rgp2d9RLGabFVlZVqFr8)
2025-07-23 16:09:52,859 - INFO - Syncing note: Kudofoto.md
2025-07-23 16:09:55,614 - ERROR - Error creating Google Doc Kudofoto: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1g4fKf4y7gpjE66kC0xG65xBHotH6XXooQ5y7cYdNwJk:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[2].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[2].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[2].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:09:55,614 - INFO - Syncing note: Solutions & System Designs & Design Patterns.md
2025-07-23 16:10:04,805 - INFO - Created Google Doc: Solutions & System Designs & Design Patterns (ID: 1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0)
2025-07-23 16:10:04,805 - INFO - Processing image: Pasted image 20240904085651.png
2025-07-23 16:10:09,443 - INFO - Uploaded media file: Pasted image 20240904085651.png (ID: 1BVMg7mez34UTGfq9uvmKudZ4WPhisAcl)
2025-07-23 16:10:12,380 - INFO - Inserted image Pasted image 20240904085651.png into document
2025-07-23 16:10:12,381 - INFO - Successfully inserted image: Pasted image 20240904085651.png
2025-07-23 16:10:12,381 - INFO - Processing image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 16:10:17,426 - INFO - Uploaded media file: telegram-cloud-photo-size-5-6311899726957623527-y.jpg (ID: 1x2VcZy6THY-81LcLKzSsEHYBadWaxa0C)
2025-07-23 16:10:21,011 - INFO - Inserted image telegram-cloud-photo-size-5-6311899726957623527-y.jpg into document
2025-07-23 16:10:21,011 - INFO - Successfully inserted image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 16:10:21,011 - INFO - Processing image: Pasted image 20240425163928.png
2025-07-23 16:10:25,666 - INFO - Uploaded media file: Pasted image 20240425163928.png (ID: 1k5-DoD2p-XBgHKd_C_dhX3i5KZPuWcz2)
2025-07-23 16:10:28,209 - INFO - Inserted image Pasted image 20240425163928.png into document
2025-07-23 16:10:28,210 - INFO - Successfully inserted image: Pasted image 20240425163928.png
2025-07-23 16:10:28,210 - INFO - Processing image: Pasted image 20240425163824.png
2025-07-23 16:10:34,830 - INFO - Uploaded media file: Pasted image 20240425163824.png (ID: 1u7-tuzGO0ynxOweUx_lgMLC-ScbmuqI3)
2025-07-23 16:10:37,657 - INFO - Inserted image Pasted image 20240425163824.png into document
2025-07-23 16:10:37,657 - INFO - Successfully inserted image: Pasted image 20240425163824.png
2025-07-23 16:10:37,657 - INFO - Processing image: Pasted image 20240903230309.png
2025-07-23 16:10:43,435 - INFO - Uploaded media file: Pasted image 20240903230309.png (ID: 1-ULqEwisqMj8Jm_-frEiMoQ1FZCrRIkA)
2025-07-23 16:10:47,132 - INFO - Inserted image Pasted image 20240903230309.png into document
2025-07-23 16:10:47,132 - INFO - Successfully inserted image: Pasted image 20240903230309.png
2025-07-23 16:10:47,132 - INFO - Processing image: Pasted image 20240903230303.png
2025-07-23 16:10:51,938 - INFO - Uploaded media file: Pasted image 20240903230303.png (ID: 1dfaXRz7NADOcw_z8k6_peTOtb-_On8p9)
2025-07-23 16:10:55,418 - INFO - Inserted image Pasted image 20240903230303.png into document
2025-07-23 16:10:55,418 - INFO - Successfully inserted image: Pasted image 20240903230303.png
2025-07-23 16:10:55,419 - INFO - Processing image: Pasted image 20240903223244.png
2025-07-23 16:10:59,932 - INFO - Uploaded media file: Pasted image 20240903223244.png (ID: 1DGrQy-x24WrXKidryCmZ4uSQQjHmrttQ)
2025-07-23 16:11:02,443 - INFO - Inserted image Pasted image 20240903223244.png into document
2025-07-23 16:11:02,444 - INFO - Successfully inserted image: Pasted image 20240903223244.png
2025-07-23 16:11:02,444 - INFO - Processing image: Pasted image 20240927232457.png
2025-07-23 16:11:08,432 - INFO - Uploaded media file: Pasted image 20240927232457.png (ID: 1TFbfmMgqpfZ9k3JsrUl6sH0esuPQHCZ0)
2025-07-23 16:11:10,359 - INFO - Inserted image Pasted image 20240927232457.png into document
2025-07-23 16:11:10,359 - INFO - Successfully inserted image: Pasted image 20240927232457.png
2025-07-23 16:11:10,359 - INFO - Processing image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 16:11:17,983 - INFO - Uploaded media file: bddf3546-c720-4313-9046-36d8c4a97019.png (ID: 10cMIzMIg2lOH1BMgXjdouVK7122XPRw3)
2025-07-23 16:11:20,561 - INFO - Inserted image bddf3546-c720-4313-9046-36d8c4a97019.png into document
2025-07-23 16:11:20,561 - INFO - Successfully inserted image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 16:11:20,561 - INFO - Processing image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 16:11:25,530 - INFO - Uploaded media file: 390dd032e50c3364eec22e71a19b2113_MD5.jpg (ID: 19wcOceGbjetRBXKGwOBIvDZ4qSDFN150)
2025-07-23 16:11:28,380 - INFO - Inserted image 390dd032e50c3364eec22e71a19b2113_MD5.jpg into document
2025-07-23 16:11:28,380 - INFO - Successfully inserted image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 16:11:28,380 - INFO - Processing image: Untitled 3.png
2025-07-23 16:11:34,171 - INFO - Uploaded media file: Untitled 3.png (ID: 1HXQWztYeZIkQ5C8epKrBKw0GLiV8yMY6)
2025-07-23 16:11:37,599 - INFO - Inserted image Untitled 3.png into document
2025-07-23 16:11:37,599 - INFO - Successfully inserted image: Untitled 3.png
2025-07-23 16:11:37,600 - INFO - Processing image: Pasted image 20241012194316.png
2025-07-23 16:11:42,662 - INFO - Uploaded media file: Pasted image 20241012194316.png (ID: 1eTfURQLf10pCjgF5oDFec8afvyJeOqsN)
2025-07-23 16:11:45,203 - INFO - Inserted image Pasted image 20241012194316.png into document
2025-07-23 16:11:45,203 - INFO - Successfully inserted image: Pasted image 20241012194316.png
2025-07-23 16:11:45,203 - INFO - Processing image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 16:11:49,630 - INFO - Uploaded media file: 390dd032e50c3364eec22e71a19b2113_MD5.jpg (ID: 17ZXdEoJqzIowcQh72SFOYfLFZ0RkVemS)
2025-07-23 16:11:51,897 - INFO - Inserted image 390dd032e50c3364eec22e71a19b2113_MD5.jpg into document
2025-07-23 16:11:51,898 - INFO - Successfully inserted image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 16:11:51,898 - INFO - Processing image: Pasted image 20240904085651.png
2025-07-23 16:11:55,668 - INFO - Uploaded media file: Pasted image 20240904085651.png (ID: 1XOrWMUA1g37goqDr_GMdgPUsdb3Vn_5T)
2025-07-23 16:11:58,311 - INFO - Inserted image Pasted image 20240904085651.png into document
2025-07-23 16:11:58,312 - INFO - Successfully inserted image: Pasted image 20240904085651.png
2025-07-23 16:11:58,312 - INFO - Processing image: Pasted image 20240903223244.png
2025-07-23 16:12:02,871 - INFO - Uploaded media file: Pasted image 20240903223244.png (ID: 1HuVW4eqf5HhA59WlrYh5ZibH_ld-Teqj)
2025-07-23 16:12:05,323 - INFO - Inserted image Pasted image 20240903223244.png into document
2025-07-23 16:12:05,323 - INFO - Successfully inserted image: Pasted image 20240903223244.png
2025-07-23 16:12:05,324 - INFO - Processing image: Pasted image 20240903230309.png
2025-07-23 16:12:10,045 - INFO - Uploaded media file: Pasted image 20240903230309.png (ID: 1Uip24fd7KUvsT1phhBPlkOYqUBkaLhB7)
2025-07-23 16:12:13,366 - INFO - Inserted image Pasted image 20240903230309.png into document
2025-07-23 16:12:13,367 - INFO - Successfully inserted image: Pasted image 20240903230309.png
2025-07-23 16:12:13,367 - INFO - Processing image: Pasted image 20240903230303.png
2025-07-23 16:12:23,916 - INFO - Uploaded media file: Pasted image 20240903230303.png (ID: 1cjjJMo53qYi-6oi7zMWSiLA97e2XzUZe)
2025-07-23 16:12:26,476 - INFO - Inserted image Pasted image 20240903230303.png into document
2025-07-23 16:12:26,477 - INFO - Successfully inserted image: Pasted image 20240903230303.png
2025-07-23 16:12:26,477 - INFO - Processing image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 16:12:32,013 - INFO - Uploaded media file: bddf3546-c720-4313-9046-36d8c4a97019.png (ID: 1FHAo_wICm6U86hkecX9oU-PZQSlEF7db)
2025-07-23 16:12:34,758 - INFO - Inserted image bddf3546-c720-4313-9046-36d8c4a97019.png into document
2025-07-23 16:12:34,758 - INFO - Successfully inserted image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 16:12:34,758 - INFO - Processing image: Pasted image 20240927232457.png
2025-07-23 16:12:39,207 - INFO - Uploaded media file: Pasted image 20240927232457.png (ID: 1-MZwRMJW8bBP5UM_VNREH3H_xzahgsrJ)
2025-07-23 16:12:40,848 - INFO - Inserted image Pasted image 20240927232457.png into document
2025-07-23 16:12:40,849 - INFO - Successfully inserted image: Pasted image 20240927232457.png
2025-07-23 16:12:40,849 - INFO - Processing image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 16:12:45,612 - INFO - Uploaded media file: telegram-cloud-photo-size-5-6311899726957623527-y.jpg (ID: 1EKLrjez7nllmKASntu0TCn8btkG8K4Lp)
2025-07-23 16:12:48,140 - INFO - Inserted image telegram-cloud-photo-size-5-6311899726957623527-y.jpg into document
2025-07-23 16:12:48,140 - INFO - Successfully inserted image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 16:12:48,140 - INFO - Processing image: Pasted image 20240425163928.png
2025-07-23 16:12:52,633 - INFO - Uploaded media file: Pasted image 20240425163928.png (ID: 1pBvvDB15NkxLJ4CGCuYG_CAugNKH-Uu1)
2025-07-23 16:12:55,065 - INFO - Inserted image Pasted image 20240425163928.png into document
2025-07-23 16:12:55,066 - INFO - Successfully inserted image: Pasted image 20240425163928.png
2025-07-23 16:12:55,066 - INFO - Processing image: Pasted image 20240425163824.png
2025-07-23 16:12:59,250 - INFO - Uploaded media file: Pasted image 20240425163824.png (ID: 1uch3uJOEX8PCyedJdLU1-ZUxTuxY3x92)
2025-07-23 16:13:02,008 - INFO - Inserted image Pasted image 20240425163824.png into document
2025-07-23 16:13:02,008 - INFO - Successfully inserted image: Pasted image 20240425163824.png
2025-07-23 16:13:02,008 - INFO - Processing image: Pasted image 20241012194316.png
2025-07-23 16:13:07,568 - INFO - Uploaded media file: Pasted image 20241012194316.png (ID: 1pKRFOKdZqtxJ-8cP367ZpsL8l6FCZV7r)
2025-07-23 16:13:10,017 - INFO - Inserted image Pasted image 20241012194316.png into document
2025-07-23 16:13:10,017 - INFO - Successfully inserted image: Pasted image 20241012194316.png
2025-07-23 16:13:10,018 - INFO - Syncing note: Domain knowledge.md
2025-07-23 16:13:14,192 - INFO - Created Google Doc: Domain knowledge (ID: 1xepvDvs1xsuTuWFXb61bXrMxmFV1miDYe3iZd5XG2CA)
2025-07-23 16:13:14,192 - INFO - Syncing note: VPN - Proxy - Firewall.md
2025-07-23 16:13:50,669 - ERROR - Error creating Google Doc VPN - Proxy - Firewall: <HttpError 500 when requesting https://docs.googleapis.com/v1/documents?alt=json returned "Internal error encountered.". Details: "Internal error encountered.">
2025-07-23 16:13:50,671 - INFO - Syncing note: Cách làm sạch và bảo quản boots.md
2025-07-23 16:13:54,044 - INFO - Created Google Doc: Cách làm sạch và bảo quản boots (ID: 11KpYOmykbk8_N7-CmItG8rd-2E6ZqOyW3o1Qp7GbZMI)
2025-07-23 16:13:54,044 - INFO - Processing image: Untitled 2 6.png
2025-07-23 16:13:58,012 - INFO - Uploaded media file: Untitled 2 6.png (ID: 13FDZA05x6LZFPXoCWmn7s0_MCZ9Dubzj)
2025-07-23 16:14:00,721 - INFO - Inserted image Untitled 2 6.png into document
2025-07-23 16:14:00,721 - INFO - Successfully inserted image: Untitled 2 6.png
2025-07-23 16:14:00,721 - INFO - Processing image: Untitled 1 9.png
2025-07-23 16:14:05,181 - INFO - Uploaded media file: Untitled 1 9.png (ID: 1ERmvjwsTJgxP5FThseM_0k5fUnev1XBF)
2025-07-23 16:14:07,940 - INFO - Inserted image Untitled 1 9.png into document
2025-07-23 16:14:07,940 - INFO - Successfully inserted image: Untitled 1 9.png
2025-07-23 16:14:07,940 - INFO - Processing image: Untitled 16.png
2025-07-23 16:14:13,672 - INFO - Uploaded media file: Untitled 16.png (ID: 1CLRtvemBdWXXHAB5ANbyDbNHMjMQVYce)
2025-07-23 16:14:15,510 - INFO - Inserted image Untitled 16.png into document
2025-07-23 16:14:15,510 - INFO - Successfully inserted image: Untitled 16.png
2025-07-23 16:14:15,510 - INFO - Syncing note: English.md
2025-07-23 16:14:19,781 - INFO - Created Google Doc: English (ID: 1Qc9r_WJsNzjhf0u7u6ZgRJX3ceTztZTrOAwKuDy4tMQ)
2025-07-23 16:14:19,782 - INFO - Processing image: 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png
2025-07-23 16:14:23,895 - INFO - Uploaded media file: 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png (ID: 1G_V_NGVX1-jY70yrOZr5Zf6uRhm6UTax)
2025-07-23 16:14:27,186 - INFO - Inserted image 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png into document
2025-07-23 16:14:27,187 - INFO - Successfully inserted image: 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png
2025-07-23 16:14:27,187 - INFO - Syncing note: Kotlin.md
2025-07-23 16:14:31,875 - INFO - Created Google Doc: Kotlin (ID: 10YdOHGuB6Rub8_k-0M8b_UsoXzwj4k7zaerUb1avbX0)
2025-07-23 16:14:31,876 - INFO - Syncing note: Golang.md
2025-07-23 16:14:37,650 - INFO - Created Google Doc: Golang (ID: 1KYmV_TUuazmrcY8v-C2-eh0A3yLDeeqoYjQexUkPIlQ)
2025-07-23 16:14:37,650 - INFO - Processing image: Basic Golang Project.jpg
2025-07-23 16:14:42,697 - INFO - Uploaded media file: Basic Golang Project.jpg (ID: 15S4lbWCP74oN2upTxkF7HdfzPsUZV8BV)
2025-07-23 16:14:45,171 - INFO - Inserted image Basic Golang Project.jpg into document
2025-07-23 16:14:45,171 - INFO - Successfully inserted image: Basic Golang Project.jpg
2025-07-23 16:14:45,174 - INFO - Syncing note: Fresher Java Interview.md
2025-07-23 16:14:49,960 - INFO - Created Google Doc: Fresher Java Interview (ID: 1fZDNUDnifAG04XkLX9b6B08NsjEnnd5e0nWSVLBkp1I)
2025-07-23 16:14:49,960 - INFO - Syncing note: Blockchain.md
2025-07-23 16:14:53,614 - INFO - Created Google Doc: Blockchain (ID: 1_PhUtiMM40Shw1K2G7lJ0Q5Zr2yfE49EUn45BszzOB8)
2025-07-23 16:14:53,614 - INFO - Syncing note: No-code - nocode - low-code - lowcode.md
2025-07-23 16:14:58,573 - INFO - Created Google Doc: No-code - nocode - low-code - lowcode (ID: 1UD_PznJD8UNON-HN7a_wu1UDzhAuZk6eabHQoF9fl5Y)
2025-07-23 16:14:58,574 - INFO - Processing image: 1675871539203.png
2025-07-23 16:15:04,222 - INFO - Uploaded media file: 1675871539203.png (ID: 1HrngqADYngMmqsbVgiL4wkU9DOLypCNv)
2025-07-23 16:15:05,881 - ERROR - Error inserting image 1675871539203.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1UD_PznJD8UNON-HN7a_wu1UDzhAuZk6eabHQoF9fl5Y:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: There was a problem retrieving the image. The provided image should be publicly accessible, within size limit, and in supported formats.". Details: "Invalid requests[0].insertInlineImage: There was a problem retrieving the image. The provided image should be publicly accessible, within size limit, and in supported formats.">
2025-07-23 16:15:05,881 - ERROR - Failed to insert image: 1675871539203.png
2025-07-23 16:15:05,881 - INFO - Syncing note: React - Next.md
2025-07-23 16:15:10,200 - INFO - Created Google Doc: React - Next (ID: 1ySgDP0LGA9GS1ntkU0c51yjjDw9CsUQ0PO10X8YOIO8)
2025-07-23 16:15:10,201 - INFO - Syncing note: SAP - Systems, Applications, and Products.md
2025-07-23 16:15:14,413 - INFO - Created Google Doc: SAP - Systems, Applications, and Products (ID: 1oBhJwy8hZFyKO8voXmztMxf0qzPLxzvBONi5n-CpFjU)
2025-07-23 16:15:14,414 - INFO - Syncing note: Airblade - AB - Air blade.md
2025-07-23 16:15:17,696 - INFO - Created Google Doc: Airblade - AB - Air blade (ID: 1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg)
2025-07-23 16:15:17,697 - INFO - Processing image: Pasted image 20240505160601.png
2025-07-23 16:15:23,788 - INFO - Uploaded media file: Pasted image 20240505160601.png (ID: 19vHqQRDhIXQnafrFsSIdtF5v-8Tx0oMu)
2025-07-23 16:15:25,974 - INFO - Inserted image Pasted image 20240505160601.png into document
2025-07-23 16:15:25,975 - INFO - Successfully inserted image: Pasted image 20240505160601.png
2025-07-23 16:15:25,975 - INFO - Processing image: Pasted image 20240502203227.png
2025-07-23 16:15:30,083 - INFO - Uploaded media file: Pasted image 20240502203227.png (ID: 184yBiOBwiC1BEjPcUZUBkLAbxDRWF6Dx)
2025-07-23 16:15:32,120 - INFO - Inserted image Pasted image 20240502203227.png into document
2025-07-23 16:15:32,120 - INFO - Successfully inserted image: Pasted image 20240502203227.png
2025-07-23 16:15:32,120 - INFO - Processing image: Pasted image 20240502195226.png
2025-07-23 16:15:36,957 - INFO - Uploaded media file: Pasted image 20240502195226.png (ID: 1AfS3bqZ8UZ2tAw3CtDnazyHrVJ8rDUCO)
2025-07-23 16:15:39,048 - INFO - Inserted image Pasted image 20240502195226.png into document
2025-07-23 16:15:39,049 - INFO - Successfully inserted image: Pasted image 20240502195226.png
2025-07-23 16:15:39,049 - INFO - Processing image: Pasted image 20240502195106.png
2025-07-23 16:15:43,969 - INFO - Uploaded media file: Pasted image 20240502195106.png (ID: 1wCOr0N3Ccg733qGpvFOga0s4QxJFIvNd)
2025-07-23 16:15:46,467 - INFO - Inserted image Pasted image 20240502195106.png into document
2025-07-23 16:15:46,467 - INFO - Successfully inserted image: Pasted image 20240502195106.png
2025-07-23 16:15:46,467 - INFO - Processing image: Pasted image 20240502193802.png
2025-07-23 16:15:51,584 - INFO - Uploaded media file: Pasted image 20240502193802.png (ID: 1vHljhifrKfzEfVhZGhLPMG8I83-h1fOq)
2025-07-23 16:15:54,077 - INFO - Inserted image Pasted image 20240502193802.png into document
2025-07-23 16:15:54,078 - INFO - Successfully inserted image: Pasted image 20240502193802.png
2025-07-23 16:15:54,078 - INFO - Processing image: Pasted image 20240502180730.png
2025-07-23 16:15:59,285 - INFO - Uploaded media file: Pasted image 20240502180730.png (ID: 1480xkeA-YEMkHs-xceHyzWl8tt6NsGmR)
2025-07-23 16:16:01,213 - INFO - Inserted image Pasted image 20240502180730.png into document
2025-07-23 16:16:01,213 - INFO - Successfully inserted image: Pasted image 20240502180730.png
2025-07-23 16:16:01,213 - INFO - Processing image: Pasted image 20240502175940.png
2025-07-23 16:16:05,670 - INFO - Uploaded media file: Pasted image 20240502175940.png (ID: 1Z6jpvse7ChAJiT6-lK2oHXJY_QQ3Ok1b)
2025-07-23 16:16:08,217 - INFO - Inserted image Pasted image 20240502175940.png into document
2025-07-23 16:16:08,218 - INFO - Successfully inserted image: Pasted image 20240502175940.png
2025-07-23 16:16:08,218 - INFO - Processing image: Pasted image 20240502175000.png
2025-07-23 16:16:12,706 - INFO - Uploaded media file: Pasted image 20240502175000.png (ID: 1-h1sSlqTb8oNdK8mi_wevHLnvPfyVNrM)
2025-07-23 16:16:14,563 - INFO - Inserted image Pasted image 20240502175000.png into document
2025-07-23 16:16:14,564 - INFO - Successfully inserted image: Pasted image 20240502175000.png
2025-07-23 16:16:14,564 - INFO - Processing image: Pasted image 20240502173135.png
2025-07-23 16:16:18,888 - INFO - Uploaded media file: Pasted image 20240502173135.png (ID: 1Hu600W9W1IMUmk0jXwPouutZDnPIUBTl)
2025-07-23 16:16:21,532 - INFO - Inserted image Pasted image 20240502173135.png into document
2025-07-23 16:16:21,533 - INFO - Successfully inserted image: Pasted image 20240502173135.png
2025-07-23 16:16:21,533 - INFO - Processing image: Pasted image 20240502172424.png
2025-07-23 16:16:25,260 - INFO - Uploaded media file: Pasted image 20240502172424.png (ID: 16S3g6HOx_gMZ9Icj_CC-dF5Y2XAvndLU)
2025-07-23 16:16:28,030 - INFO - Inserted image Pasted image 20240502172424.png into document
2025-07-23 16:16:28,032 - INFO - Successfully inserted image: Pasted image 20240502172424.png
2025-07-23 16:16:28,032 - INFO - Syncing note: Post score algorithm - Trending algorithm.md
2025-07-23 16:16:32,014 - INFO - Created Google Doc: Post score algorithm - Trending algorithm (ID: 1zgjhuv7c9cE894QbtTCCjJ6IcLmhzwlEwD64gNotcH8)
2025-07-23 16:16:32,014 - INFO - Processing image: Pasted image 20231014200850.png
2025-07-23 16:16:35,959 - INFO - Uploaded media file: Pasted image 20231014200850.png (ID: 1Meqv2ihZPf2jTVvKS1ucSA1fF8wuYa2J)
2025-07-23 16:16:38,687 - INFO - Inserted image Pasted image 20231014200850.png into document
2025-07-23 16:16:38,687 - INFO - Successfully inserted image: Pasted image 20231014200850.png
2025-07-23 16:16:38,687 - INFO - Processing image: Pasted image 20231014200829.png
2025-07-23 16:16:43,015 - INFO - Uploaded media file: Pasted image 20231014200829.png (ID: 1djVOj-7KJ6Jo3e_MWii2dh6clUdOio0s)
2025-07-23 16:16:45,063 - INFO - Inserted image Pasted image 20231014200829.png into document
2025-07-23 16:16:45,063 - INFO - Successfully inserted image: Pasted image 20231014200829.png
2025-07-23 16:16:45,063 - INFO - Syncing note: Windows Tips.md
2025-07-23 16:16:48,220 - INFO - Created Google Doc: Windows Tips (ID: 1cJCZPhQ4il_5zGPGbEuh8Yzq3UajQXkwQn4dkmwkrqU)
2025-07-23 16:16:48,221 - INFO - Syncing note: Du lịch Huế.md
2025-07-23 16:17:41,355 - ERROR - Error creating Google Doc Du lịch Huế: <HttpError 500 when requesting https://docs.googleapis.com/v1/documents?alt=json returned "Internal error encountered.". Details: "Internal error encountered.">
2025-07-23 16:17:41,357 - INFO - Syncing note: Research websites.md
2025-07-23 16:17:44,610 - INFO - Created Google Doc: Research websites (ID: 1bEf_3LjMuf4Zth83CKalQ1ehNurHF634NJMywA5hb58)
2025-07-23 16:17:44,612 - INFO - Syncing note: Terminal UI - TUI.md
2025-07-23 16:17:47,671 - INFO - Created Google Doc: Terminal UI - TUI (ID: 1GSkX8bgTQPSZ71HdGOSyeeAhWAKxf4WZGvFFe09hVd4)
2025-07-23 16:17:47,672 - INFO - Syncing note: Các loại quần nên có trong tủ đồ.md
2025-07-23 16:17:52,118 - INFO - Created Google Doc: Các loại quần nên có trong tủ đồ (ID: 16o_Qp0KunGsHDPBXEC8IGXTCZkwl5HwO-ZVeYuKNPcU)
2025-07-23 16:17:52,119 - INFO - Processing image: Untitled 2 5.png
2025-07-23 16:17:55,654 - INFO - Uploaded media file: Untitled 2 5.png (ID: 1XEPzWgDsqiLVu8qaI-QythxFslGZqTQR)
2025-07-23 16:17:57,916 - INFO - Inserted image Untitled 2 5.png into document
2025-07-23 16:17:57,916 - INFO - Successfully inserted image: Untitled 2 5.png
2025-07-23 16:17:57,916 - INFO - Processing image: Untitled 1 8.png
2025-07-23 16:18:04,152 - INFO - Uploaded media file: Untitled 1 8.png (ID: 1xQL65i5prT573u1YuZrBi83cV7CZrA_v)
2025-07-23 16:18:07,024 - INFO - Inserted image Untitled 1 8.png into document
2025-07-23 16:18:07,025 - INFO - Successfully inserted image: Untitled 1 8.png
2025-07-23 16:18:07,025 - INFO - Processing image: Untitled 15.png
2025-07-23 16:18:11,975 - INFO - Uploaded media file: Untitled 15.png (ID: 1Y7No72BnqKOp9_fHTGcnfwXMXw7MkFxu)
2025-07-23 16:18:14,884 - INFO - Inserted image Untitled 15.png into document
2025-07-23 16:18:14,884 - INFO - Successfully inserted image: Untitled 15.png
2025-07-23 16:18:14,885 - INFO - Syncing note: Management.md
2025-07-23 16:18:18,321 - INFO - Created Google Doc: Management (ID: 1TjvPRlAt6KdXilAtl1mxWyD1i0VNQcjLblShX_0Jfqg)
2025-07-23 16:18:18,321 - INFO - Syncing note: Monitor server.md
2025-07-23 16:18:22,838 - INFO - Created Google Doc: Monitor server (ID: 18QAkrY_8inTaG-ST2ex44R3RH0oyvDjNZO2lmXeCR0w)
2025-07-23 16:18:22,838 - INFO - Syncing note: English with LLM.md
2025-07-23 16:18:26,153 - INFO - Created Google Doc: English with LLM (ID: 11sE14ivV3qs4448wFpb2jaCglZQo2yB6wOYsEIS7vVk)
2025-07-23 16:18:26,153 - INFO - Syncing note: Network.md
2025-07-23 16:18:31,314 - INFO - Created Google Doc: Network (ID: 1cVxBxhtezGm1pWg-J-rhO5tzscXGwYb3V5zB-44daK0)
2025-07-23 16:18:31,314 - INFO - Syncing note: Python.md
2025-07-23 16:18:34,674 - INFO - Created Google Doc: Python (ID: 1lnfHHel5g-wtPw_sqyKWe35IyfgmLprjNP4-fkJDHsg)
2025-07-23 16:18:34,675 - INFO - Syncing note: Tạo video bằng các tool AI.md
2025-07-23 16:18:39,032 - INFO - Created Google Doc: Tạo video bằng các tool AI (ID: 1Q4-Lt7dp8C_SdiPV5BEkgHPrqrL4AoqLFCjjSsTAjSY)
2025-07-23 16:18:39,032 - INFO - Syncing note: English with LLM - Irregular verbs.md
2025-07-23 16:18:44,199 - INFO - Created Google Doc: English with LLM - Irregular verbs (ID: 196VDsUjgp6sZIsKxs-WR_6kl4Is_Zb3WJYrPr160gno)
2025-07-23 16:18:44,200 - INFO - Syncing note: Work.md
2025-07-23 16:18:47,172 - INFO - Created Google Doc: Work (ID: 1saDbL2T3xy0EAUzpmIsohg51IBO1NudsLWUjetUeVyU)
2025-07-23 16:18:47,173 - INFO - Syncing note: Chuyến Du Lịch Công Ty Inception Labs 2025.md
2025-07-23 16:18:51,151 - INFO - Created Google Doc: Chuyến Du Lịch Công Ty Inception Labs 2025 (ID: 1TMTticn-h1N5BTclZhkQ4PoEdzn8XGlT87Bz6T80m6s)
2025-07-23 16:18:51,153 - INFO - Syncing note: Dagger & Koin.md
2025-07-23 16:18:55,229 - INFO - Created Google Doc: Dagger & Koin (ID: 1WU-SXX8_ZYJapUhL4NfFSyb1R7UaqSLu5Q4VidMJc8s)
2025-07-23 16:18:55,229 - INFO - Syncing note: SolidJS.md
2025-07-23 16:18:59,774 - INFO - Created Google Doc: SolidJS (ID: 1UMMmHAB7OoKqKAB7APlzexinKnQMz-T6YYB2KpzdjK0)
2025-07-23 16:18:59,774 - INFO - Syncing note: Fresher Back-end Interview.md
2025-07-23 16:19:04,041 - INFO - Created Google Doc: Fresher Back-end Interview (ID: 1nIJsJZ4F5UjH8Z3fOyWmtruAxvjXo6p1Rpsa0d3ZzDU)
2025-07-23 16:19:04,041 - INFO - Syncing note: CSS.md
2025-07-23 16:19:08,388 - INFO - Created Google Doc: CSS (ID: 1hAMbm0rWWZtih05lQWiuCbChZfNQfBFV_sjMyjN0lP4)
2025-07-23 16:19:08,389 - INFO - Syncing note: 25 phương pháp giúp bạn ngừng overthinking.md
2025-07-23 16:19:12,810 - INFO - Created Google Doc: 25 phương pháp giúp bạn ngừng overthinking (ID: 1jKmq3wsPQ5vq52eRMZiJ08bquAfweSEf_KgXoZblXUY)
2025-07-23 16:19:12,811 - INFO - Syncing note: Working.md
2025-07-23 16:19:17,830 - INFO - Created Google Doc: Working (ID: 1kXijkaDqwqY5s6En5EhGhuIBdM_k_W3LFIgH6ymr9Lk)
2025-07-23 16:19:17,831 - INFO - Syncing note: Thống kê tủ đồ hiện tại.md
2025-07-23 16:19:22,005 - INFO - Created Google Doc: Thống kê tủ đồ hiện tại (ID: 15PFC-hz9OhmbDLdDYdK9Yjt0dldu72d_v_gDPLIMhp0)
2025-07-23 16:19:22,005 - INFO - Syncing note: Note lại từ Huyền Chip.md
2025-07-23 16:19:25,976 - INFO - Created Google Doc: Note lại từ Huyền Chip (ID: 11mD1mpZot8WIfmRyJ5Rprlc9NfVVflomv42hEMiC34c)
2025-07-23 16:19:25,976 - INFO - Syncing note: Công cụ học tiếng Anh.md
2025-07-23 16:19:28,960 - INFO - Created Google Doc: Công cụ học tiếng Anh (ID: 1c2GLz8NhZOc4OnrY61DkCPZ3Bq3ewKglaP91_4hiCeE)
2025-07-23 16:19:28,960 - INFO - Syncing note: Software Engineer Roadmap 2025 - The Complete Guide.md
2025-07-23 16:19:33,379 - INFO - Created Google Doc: Software Engineer Roadmap 2025 - The Complete Guide (ID: 1hqThsX0aF2vu2_9urqFed8WJ62Yx0Sow_X0dVR26ncE)
2025-07-23 16:19:33,379 - INFO - Syncing note: English with LLM - Mệnh đề quan hệ.md
2025-07-23 16:19:37,298 - INFO - Created Google Doc: English with LLM - Mệnh đề quan hệ (ID: 1HRPjPJw_U7FAEfkauJGkHvFVgwgTymW2Jvjcqnz2HT8)
2025-07-23 16:19:37,299 - INFO - Syncing note: Development documentations.md
2025-07-23 16:19:41,790 - INFO - Created Google Doc: Development documentations (ID: 1iuVuCo3SHgz8UuN3QiStxRUPo_yH6m5Qc_AIYycLX3k)
2025-07-23 16:19:41,790 - INFO - Syncing note: Freelance.md
2025-07-23 16:19:45,992 - INFO - Created Google Doc: Freelance (ID: 1i0YG2ey7N8mVkIjHtHe3oInV5J0ZppDrAk-JH8lbZVg)
2025-07-23 16:19:45,992 - INFO - Syncing note: Phượt.md
2025-07-23 16:19:51,512 - INFO - Created Google Doc: Phượt (ID: 1rwhOUR0ti4paqPsdHc6xZ1XEfi6p-6eVuF2Lzssg-YE)
2025-07-23 16:19:51,513 - INFO - Syncing note: Luận bàn về async.md
2025-07-23 16:19:55,525 - INFO - Created Google Doc: Luận bàn về async (ID: 1cED9AIq4sRLUAq9glev7XTrOTkmVhXc5OLp01sc3E_Y)
2025-07-23 16:19:55,525 - INFO - Syncing note: Clean Code notes.md
2025-07-23 16:19:59,358 - INFO - Created Google Doc: Clean Code notes (ID: 1HuYLce74EdhM0zxs6hwkc_YcknFhbF3OwX1P41Ygdis)
2025-07-23 16:19:59,359 - INFO - Syncing note: LLM promt engineering.md
2025-07-23 16:20:02,581 - INFO - Created Google Doc: LLM promt engineering (ID: 1X4vpT7asYV6LcTz4kVnSN2jvatR9f8Djp_sIqLJsvdc)
2025-07-23 16:20:02,581 - INFO - Syncing note: Fullstack - Full-stack.md
2025-07-23 16:20:05,818 - INFO - Created Google Doc: Fullstack - Full-stack (ID: 1-cfEffa7-Gtw0XaWSANuxNModd_r3zMsucXQKBZlQFM)
2025-07-23 16:20:05,818 - INFO - Syncing note: Life.md
2025-07-23 16:20:10,731 - INFO - Created Google Doc: Life (ID: 10U1ufwoI6C_mVZcmweNGNcgtetvNj-b4NzhUfey9lGs)
2025-07-23 16:20:10,732 - INFO - Syncing note: Business.md
2025-07-23 16:20:15,094 - INFO - Created Google Doc: Business (ID: 1sAcpcGwWWzC0CLpESrrvR56gwID31be2cvEPn1H2AZg)
2025-07-23 16:20:15,094 - INFO - Syncing note: ARBO.md
2025-07-23 16:20:18,426 - INFO - Created Google Doc: ARBO (ID: 1peGdm3tkhZbgWEYdsdM06I6dA01dzZlDWhS9kGofmj8)
2025-07-23 16:20:18,428 - INFO - Processing image: Untitled 4 2.png
2025-07-23 16:20:23,296 - INFO - Uploaded media file: Untitled 4 2.png (ID: 1SSmhf8SDT3EyNTDDbn7FH9wzvkg2wrkF)
2025-07-23 16:20:26,266 - INFO - Inserted image Untitled 4 2.png into document
2025-07-23 16:20:26,266 - INFO - Successfully inserted image: Untitled 4 2.png
2025-07-23 16:20:26,266 - INFO - Processing image: Untitled 3 4.png
2025-07-23 16:20:31,866 - INFO - Uploaded media file: Untitled 3 4.png (ID: 1IxtfsRZI_UulU08TwIsVjBwjTNGOb1L9)
2025-07-23 16:20:34,203 - INFO - Found 2 markdown files in Obsidian vault
2025-07-23 16:20:34,222 - INFO - Configuration file created: /var/folders/gg/n96mjvnn331by57zqfwkj4jc0000gn/T/tmpl5tkq2lm.json
2025-07-23 16:20:34,637 - INFO - Inserted image Untitled 3 4.png into document
2025-07-23 16:20:34,638 - INFO - Successfully inserted image: Untitled 3 4.png
2025-07-23 16:20:34,638 - INFO - Processing image: Untitled 2 7.png
2025-07-23 16:20:38,914 - INFO - Uploaded media file: Untitled 2 7.png (ID: 1xe_8fd-SjQFVYSIR92eLX03RFCJjKE0H)
2025-07-23 16:20:41,864 - INFO - Inserted image Untitled 2 7.png into document
2025-07-23 16:20:41,864 - INFO - Successfully inserted image: Untitled 2 7.png
2025-07-23 16:20:41,864 - INFO - Syncing note: Note câu hỏi phỏng vấn Laravel.md
2025-07-23 16:20:46,421 - INFO - Created Google Doc: Note câu hỏi phỏng vấn Laravel (ID: 1e_Ak4q9s2MxplPlaoGbYGt7y4Zzzw5g6b8nZUzCDn5A)
2025-07-23 16:20:46,421 - INFO - Processing image: Screenshot 2023-05-27 144607 1.png
2025-07-23 16:20:51,576 - INFO - Uploaded media file: Screenshot 2023-05-27 144607 1.png (ID: 1fNqpl_OCBRSYkibWH9TXSXDYnneODe4R)
2025-07-23 16:20:53,827 - INFO - Inserted image Screenshot 2023-05-27 144607 1.png into document
2025-07-23 16:20:53,828 - INFO - Successfully inserted image: Screenshot 2023-05-27 144607 1.png
2025-07-23 16:20:53,828 - INFO - Syncing note: viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow.md
2025-07-23 16:20:58,795 - INFO - Created Google Doc: viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow (ID: 1AEOQ3WP2ZZ6aTEm-CFp_XRh-e3wT1FvTsx8bJga7Hhw)
2025-07-23 16:20:58,795 - INFO - Syncing note: Machine learning - Deep Learning - AI - ML - DL.md
2025-07-23 16:21:03,137 - INFO - Created Google Doc: Machine learning - Deep Learning - AI - ML - DL (ID: 1zeW3XyySPpZ_RQXfAVP_yqxd4fcjyq4AoAIyOfXf75Y)
2025-07-23 16:21:03,138 - INFO - Syncing note: Note ebook Thuật toán của thầy Lê Minh Hoàng.md
2025-07-23 16:21:06,174 - INFO - Created Google Doc: Note ebook Thuật toán của thầy Lê Minh Hoàng (ID: 1B_0BPHn5hiw4AT_ME6Ti07JbqC7jIMkRlsD6ufaJlHM)
2025-07-23 16:21:06,175 - INFO - Syncing note: English with LLM - Câu nhấn mạnh.md
2025-07-23 16:21:09,542 - INFO - Created Google Doc: English with LLM - Câu nhấn mạnh (ID: 1DttWidv1R-s0S4WCTD1Q34rJoyE2SOMiDVqLPwvoIyg)
2025-07-23 16:21:09,543 - INFO - Syncing note: Nginx.md
2025-07-23 16:21:12,436 - INFO - Created Google Doc: Nginx (ID: 1WBbdWZ5ERcj9vobhFHwR5xGS7iGZCMW2sNsp7v_VRso)
2025-07-23 16:21:12,436 - INFO - Syncing note: Chỗ mua đồ.md
2025-07-23 16:21:16,365 - INFO - Created Google Doc: Chỗ mua đồ (ID: 10QfLEh9t38hQ9GKWhNfh_DCwuEus-n9WIxSel7tgmY8)
2025-07-23 16:21:16,366 - INFO - Syncing note: AI support for coding.md
2025-07-23 16:21:20,564 - INFO - Created Google Doc: AI support for coding (ID: 1dIJaVIUVBpseT7dx8S7_RRwF_1H6P4QVRhfI5Qhcd7Q)
2025-07-23 16:21:20,565 - INFO - Syncing note: Cách đặt câu hỏi cho ChatGPT.md
2025-07-23 16:21:23,867 - INFO - Created Google Doc: Cách đặt câu hỏi cho ChatGPT (ID: 1rP9LemAo2nvIkDEcuAhYJaD_z1sV7kY9Ow9gnLFCc5Q)
2025-07-23 16:21:23,867 - INFO - Syncing note: English with LLM - Danh sách 200 từ vựng cần thiết trong TOEIC.md
2025-07-23 16:21:29,447 - INFO - Created Google Doc: English with LLM - Danh sách 200 từ vựng cần thiết trong TOEIC (ID: 1UE6czEghOZGcqHhKM0LRR31_c8OwUmMmfePBoKCnwbI)
2025-07-23 16:21:29,448 - INFO - Syncing note: Râm Generation.md
2025-07-23 16:21:33,179 - INFO - Created Google Doc: Râm Generation (ID: 1peqAjdW8UFqJyzYH9qNDC55JKYpye2aRtAM09iHnWP8)
2025-07-23 16:21:33,179 - INFO - Syncing note: Ollama.md
2025-07-23 16:21:36,578 - INFO - Created Google Doc: Ollama (ID: 1r8Hgvo0k_U6wtDcK-x2OYn98hylJvv4tU0h3uJw-Ii8)
2025-07-23 16:21:36,578 - INFO - Syncing note: English with LLM - Từ nối.md
2025-07-23 16:21:40,022 - INFO - Created Google Doc: English with LLM - Từ nối (ID: 1qdD6Z0MJftKSiG4UnATn0cJeYYL8UoWEmeUbe9YA2Xw)
2025-07-23 16:21:40,023 - INFO - Syncing note: Sống Platform.md
2025-07-23 16:21:43,026 - ERROR - Error creating Google Doc Sống Platform: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1IfyIKvYyLZpCj0nWZaJte1PbplMHb2gc6u_Ekpql8CM:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[3].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[9].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[3].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[3].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[9].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[9].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:21:43,026 - INFO - Syncing note: Top 10 câu hỏi phỏng vấn System Design và Microservices.md
2025-07-23 16:21:46,372 - ERROR - Error creating Google Doc Top 10 câu hỏi phỏng vấn System Design và Microservices: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/17iLfjoCtilE9_uw2lDsVhFjljXCUrj0CdQ_WMQaUlsg:batchUpdate?alt=json returned "Invalid requests[48].insertText: The insertion index cannot be within a grapheme cluster.". Details: "Invalid requests[48].insertText: The insertion index cannot be within a grapheme cluster.">
2025-07-23 16:21:46,372 - INFO - Syncing note: Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai).md
2025-07-23 16:21:49,974 - INFO - Created Google Doc: Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai) (ID: 1dretCgwBpuHnPYP052azgfy1aQJ33z1h-YnTAAQ4gEA)
2025-07-23 16:21:49,974 - INFO - Syncing note: Kho chung IT.md
2025-07-23 16:21:53,473 - INFO - Created Google Doc: Kho chung IT (ID: 1WnQ7GECBMR999Nb22sXEj3i1Bj4-32Iff3VSWggrUvI)
2025-07-23 16:21:53,473 - INFO - Syncing note: Target of users in a workspace.md
2025-07-23 16:21:56,927 - INFO - Created Google Doc: Target of users in a workspace (ID: 1TJqv1qCERtN8Fnn6Io8A4dzj7ZCaBaUnM0-kMHByTDM)
2025-07-23 16:21:56,927 - INFO - Syncing note: Linter.md
2025-07-23 16:22:01,719 - INFO - Created Google Doc: Linter (ID: 11snTtHIqRmaVMRPbKwCmqI10qEm0RtkYH3O9FaCr2JI)
2025-07-23 16:22:01,719 - INFO - Syncing note: Promt.md
2025-07-23 16:22:05,245 - INFO - Created Google Doc: Promt (ID: 1WcZLpqSJcOAsKHI97OQUQb6vkvyvzLZfUeLmae5Glc0)
2025-07-23 16:22:05,245 - INFO - Processing image: Untitled 1 4.png
2025-07-23 16:22:10,354 - INFO - Uploaded media file: Untitled 1 4.png (ID: 1gn-NJh3ZFSRXmNiIaXVxwrFZ5l_GwkWH)
2025-07-23 16:22:14,441 - INFO - Inserted image Untitled 1 4.png into document
2025-07-23 16:22:14,441 - INFO - Successfully inserted image: Untitled 1 4.png
2025-07-23 16:22:14,441 - INFO - Processing image: Untitled 9.png
2025-07-23 16:22:18,738 - INFO - Uploaded media file: Untitled 9.png (ID: 1HuIRWfx8q4xq3xR2_tZV5tSknLUtXaGo)
2025-07-23 16:22:20,662 - INFO - Inserted image Untitled 9.png into document
2025-07-23 16:22:20,663 - INFO - Successfully inserted image: Untitled 9.png
2025-07-23 16:22:20,663 - INFO - Syncing note: Rust.md
2025-07-23 16:22:24,100 - INFO - Created Google Doc: Rust (ID: 1Zks-6eybOnNcGm8t5WKqjn0MgGJOygiyrCPEdJuObGU)
2025-07-23 16:22:24,100 - INFO - Syncing note: Du lịch.md
2025-07-23 16:22:27,011 - INFO - Created Google Doc: Du lịch (ID: 1mbjLmI1Lch4eWxH9D4klZfMA6ABmlN6Oom83dfFxmus)
2025-07-23 16:22:27,011 - INFO - Syncing note: VPS - Hosting.md
2025-07-23 16:22:30,151 - INFO - Created Google Doc: VPS - Hosting (ID: 1b2gyXCHXg-kc7XsiRpP786YV1dhDA55hof9Jtu5jvOc)
2025-07-23 16:22:30,151 - INFO - Syncing note: Trải nghiệm.md
2025-07-23 16:22:33,548 - INFO - Created Google Doc: Trải nghiệm (ID: 1Eo0HZ3a3_tiDLqqO2ojF1El293W23t9juyvZ0h_vQ2w)
2025-07-23 16:22:33,548 - INFO - Syncing note: MySQL.md
2025-07-23 16:22:37,446 - INFO - Created Google Doc: MySQL (ID: 1-EeL5eIfJQ9N8p3JKKjVMKoZ1H9OKPRn3x7yjLXzrEc)
2025-07-23 16:22:37,447 - INFO - Syncing note: Java Spring.md
2025-07-23 16:22:41,358 - INFO - Created Google Doc: Java Spring (ID: 1wcTjyl-UGQEM2IAp6lHrsVdzLWK328YH4_f6ck1-Ayg)
2025-07-23 16:22:41,360 - INFO - Syncing note: Chiến lược backup dữ liệu 3-2-1.md
2025-07-23 16:22:45,018 - INFO - Created Google Doc: Chiến lược backup dữ liệu 3-2-1 (ID: 1SFpQerAmV_BeZRVe64Jnzs3F2WAkXWAF_qK0rs2WvUQ)
2025-07-23 16:22:45,018 - INFO - Syncing note: Những thứ đã học ở Grab tech talk.md
2025-07-23 16:22:49,340 - INFO - Created Google Doc: Những thứ đã học ở Grab tech talk (ID: 1afQagzQSXBGi0rCZBAiMaiksWzVh6qdCRZX-YdRj2G0)
2025-07-23 16:22:49,341 - INFO - Syncing note: English with LLM - Cấu trúc câu phức tạp.md
2025-07-23 16:22:53,620 - INFO - Created Google Doc: English with LLM - Cấu trúc câu phức tạp (ID: 1mkvWK-mJuqkyBMYt83qQD-ts2oSTf7JS3Znb4qnpyRU)
2025-07-23 16:22:53,620 - INFO - Syncing note: viclass - 752 - Make it easier to create account for user to experience the beta system.md
2025-07-23 16:22:57,285 - INFO - Created Google Doc: viclass - 752 - Make it easier to create account for user to experience the beta system (ID: 1mu-UGPEJ1JjNpqP8qpiu8dpBc13B7Er4d64YnCL7hT4)
2025-07-23 16:22:57,286 - INFO - Syncing note: English with LLM - Loại câu.md
2025-07-23 16:22:59,788 - INFO - Found 2 markdown files in Obsidian vault
2025-07-23 16:22:59,803 - INFO - Configuration file created: /var/folders/gg/n96mjvnn331by57zqfwkj4jc0000gn/T/tmpyzpa74my.json
2025-07-23 16:23:02,175 - INFO - Created Google Doc: English with LLM - Loại câu (ID: 1_csqteX6VAJEHepV_5dPTAHANrUJJDpJHXi8CUrXh-Q)
2025-07-23 16:23:02,175 - INFO - Syncing note: Các loại trang phục cho tủ đồ.md
2025-07-23 16:23:06,261 - INFO - Created Google Doc: Các loại trang phục cho tủ đồ (ID: 17dsgCtST_6g0rIh-YKJUEZOTv8OQnEokLhn5fkNLu7c)
2025-07-23 16:23:06,261 - INFO - Syncing note: Lời khuyên.md
2025-07-23 16:23:10,274 - INFO - Created Google Doc: Lời khuyên (ID: 1OpqwVZmNi1sQiISjF37ByLVmM1_1mVy157DcGzaLbNw)
2025-07-23 16:23:10,274 - INFO - Syncing note: Airblade.md
2025-07-23 16:23:13,613 - INFO - Created Google Doc: Airblade (ID: 1u7gLIvfny55zBy1jKGD07jbYCG3pFcGc_a7JFOksth0)
2025-07-23 16:23:13,614 - INFO - Syncing note: Lộ trình thi cert AWS Solutions Architect Assoc.md
2025-07-23 16:23:16,557 - INFO - Created Google Doc: Lộ trình thi cert AWS Solutions Architect Assoc (ID: 1fhzCANB8-vA_9gCFK79upgoEp1nHNwhKxX-gkDVrd8I)
2025-07-23 16:23:16,557 - INFO - Syncing note: Các câu hỏi phỏng vấn Laravel.md
2025-07-23 16:23:20,890 - INFO - Created Google Doc: Các câu hỏi phỏng vấn Laravel (ID: 17cK5rMolx2yUaEkkZ2hOkSMQZu3Dsv-ijjNIVaNXlZI)
2025-07-23 16:23:20,891 - INFO - Processing image: Untitled 3 1.png
2025-07-23 16:23:26,094 - INFO - Uploaded media file: Untitled 3 1.png (ID: 1TQa6PLwHoGPjZjPxeWXaPnlBEhFxCdAW)
2025-07-23 16:23:28,181 - INFO - Inserted image Untitled 3 1.png into document
2025-07-23 16:23:28,182 - INFO - Successfully inserted image: Untitled 3 1.png
2025-07-23 16:23:28,182 - INFO - Processing image: Untitled 2 1.png
2025-07-23 16:23:48,412 - INFO - Uploaded media file: Untitled 2 1.png (ID: 1sNldOJMx3JL6SFODa0heSORSNrJMj3aR)
2025-07-23 16:23:51,530 - INFO - Inserted image Untitled 2 1.png into document
2025-07-23 16:23:51,530 - INFO - Successfully inserted image: Untitled 2 1.png
2025-07-23 16:23:51,530 - INFO - Processing image: Untitled 1 2.png
2025-07-23 16:23:55,826 - INFO - Uploaded media file: Untitled 1 2.png (ID: 1UrcTl4PymzQQ7O9jP8OBAeBaHVepp8Gh)
2025-07-23 16:23:58,256 - INFO - Inserted image Untitled 1 2.png into document
2025-07-23 16:23:58,257 - INFO - Successfully inserted image: Untitled 1 2.png
2025-07-23 16:23:58,258 - INFO - Processing image: Untitled 7.png
2025-07-23 16:24:03,335 - INFO - Uploaded media file: Untitled 7.png (ID: 1sPIbD2MQP6RW9MJqxMPMJNVUZZpqdAuQ)
2025-07-23 16:24:06,573 - INFO - Inserted image Untitled 7.png into document
2025-07-23 16:24:06,573 - INFO - Successfully inserted image: Untitled 7.png
2025-07-23 16:24:06,574 - INFO - Syncing note: Các loại giày nên có.md
2025-07-23 16:24:10,610 - INFO - Created Google Doc: Các loại giày nên có (ID: 1dv4dI0i_fR-CUFvH6iwYwQtGeJvSQfRKt2aFQe93mho)
2025-07-23 16:24:10,610 - INFO - Processing image: Untitled 2 4.png
2025-07-23 16:24:14,330 - INFO - Uploaded media file: Untitled 2 4.png (ID: 1jbiAlo1bhxrcsnKZ0mhuIO94gZGj2Nbf)
2025-07-23 16:24:16,954 - INFO - Inserted image Untitled 2 4.png into document
2025-07-23 16:24:16,954 - INFO - Successfully inserted image: Untitled 2 4.png
2025-07-23 16:24:16,954 - INFO - Processing image: Untitled 1 7.png
2025-07-23 16:24:20,865 - INFO - Uploaded media file: Untitled 1 7.png (ID: 1yzM3M_NcusEarqGB2ORfjWDE5wRWIKmF)
2025-07-23 16:24:23,523 - INFO - Inserted image Untitled 1 7.png into document
2025-07-23 16:24:23,523 - INFO - Successfully inserted image: Untitled 1 7.png
2025-07-23 16:24:23,523 - INFO - Processing image: Untitled 14.png
2025-07-23 16:24:27,706 - INFO - Uploaded media file: Untitled 14.png (ID: 1y7eZzaAC12jicRv7tuooG63LX_t9srvk)
2025-07-23 16:24:30,398 - INFO - Inserted image Untitled 14.png into document
2025-07-23 16:24:30,398 - INFO - Successfully inserted image: Untitled 14.png
2025-07-23 16:24:30,398 - INFO - Syncing note: Workspace.md
2025-07-23 16:24:33,659 - INFO - Created Google Doc: Workspace (ID: 1SMhJ7Ezply-zsNftFjOL7GlzxKb4uZljnOAXEpm12jg)
2025-07-23 16:24:33,659 - INFO - Syncing note: Data Analyze.md
2025-07-23 16:24:36,892 - INFO - Created Google Doc: Data Analyze (ID: 1WLnzxFA1KFMFxK-ghHLVOnu6Llww8490AtAo0E2exNQ)
2025-07-23 16:24:36,892 - INFO - Syncing note: Computer Science - Khoa học máy tính.md
2025-07-23 16:24:39,161 - INFO - Found 2 markdown files in Obsidian vault
2025-07-23 16:24:39,185 - INFO - Validated 2/2 requests
2025-07-23 16:24:39,185 - INFO - Configuration file created: /var/folders/gg/n96mjvnn331by57zqfwkj4jc0000gn/T/tmpcipd5jys.json
2025-07-23 16:24:40,384 - INFO - Created Google Doc: Computer Science - Khoa học máy tính (ID: 1vM_j54GeRhnGnx3-LF2PHLMbrZ5yw6R_VzfjmTrh-Rg)
2025-07-23 16:24:40,384 - INFO - Syncing note: English with LLM - Đảo ngữ.md
2025-07-23 16:24:43,581 - INFO - Created Google Doc: English with LLM - Đảo ngữ (ID: 1M69O2NJSSaS0jWR8i0wLUgtzKcG7PMUYPsFtB5XB4EY)
2025-07-23 16:24:43,581 - INFO - Syncing note: PHP.md
2025-07-23 16:24:47,059 - ERROR - Error creating Google Doc PHP: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1BlGknvLlgrDwyHrbKFt92ysc0v9XUFNPP9zCVhIgbP0:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[443].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[445].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[443].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[443].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[445].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[445].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:24:47,059 - INFO - Syncing note: 6 Chiến lược Prompt Hiệu quả của OpenAI.md
2025-07-23 16:24:51,966 - INFO - Created Google Doc: 6 Chiến lược Prompt Hiệu quả của OpenAI (ID: 1gVXfKbvIG3rG1Uao-gitHfIMVJ-iQr6feDx-B6BsDvA)
2025-07-23 16:24:51,967 - INFO - Syncing note: Diagrams - Vẽ sơ đồ.md
2025-07-23 16:24:56,266 - INFO - Created Google Doc: Diagrams - Vẽ sơ đồ (ID: 1FiO8V6fC-InpDwzJ-sh0QQqd0Pvq6UjQgd2A2BmcMAo)
2025-07-23 16:24:56,267 - INFO - Processing image: Untitled 5.png
2025-07-23 16:25:00,129 - INFO - Uploaded media file: Untitled 5.png (ID: 1QGfdXc_9jGwiRH5cluWhTYYSAj2QVIgZ)
2025-07-23 16:25:02,295 - INFO - Inserted image Untitled 5.png into document
2025-07-23 16:25:02,295 - INFO - Successfully inserted image: Untitled 5.png
2025-07-23 16:25:02,295 - INFO - Syncing note: Tập thể dục.md
2025-07-23 16:25:05,617 - INFO - Created Google Doc: Tập thể dục (ID: 169EGjO_P7JZv9dMpcaQi1GO-TDPXPWclpiN5vCUPG1Q)
2025-07-23 16:25:05,617 - INFO - Processing image: 112848464_2828521950762416_6675007294543947402_n.jpg
2025-07-23 16:25:10,826 - INFO - Uploaded media file: 112848464_2828521950762416_6675007294543947402_n.jpg (ID: 1YvliH2WTIGH-9egWB7Tn2ESF84ckXLLS)
2025-07-23 16:25:13,981 - INFO - Inserted image 112848464_2828521950762416_6675007294543947402_n.jpg into document
2025-07-23 16:25:13,981 - INFO - Successfully inserted image: 112848464_2828521950762416_6675007294543947402_n.jpg
2025-07-23 16:25:13,982 - INFO - Processing image: 109898156_2828522014095743_8725420912218800228_n.jpg
2025-07-23 16:25:18,430 - INFO - Uploaded media file: 109898156_2828522014095743_8725420912218800228_n.jpg (ID: 1vUcSsNKVNwP9T1vH9PWt-QloUqDYtygM)
2025-07-23 16:25:20,932 - INFO - Inserted image 109898156_2828522014095743_8725420912218800228_n.jpg into document
2025-07-23 16:25:20,933 - INFO - Successfully inserted image: 109898156_2828522014095743_8725420912218800228_n.jpg
2025-07-23 16:25:20,933 - INFO - Processing image: 109696248_2828521930762418_277213041136568379_n.jpg
2025-07-23 16:25:25,825 - INFO - Uploaded media file: 109696248_2828521930762418_277213041136568379_n.jpg (ID: 1qSay1VTSWxJJoKvCldvINKpDFs49fcRQ)
2025-07-23 16:25:28,367 - INFO - Inserted image 109696248_2828521930762418_277213041136568379_n.jpg into document
2025-07-23 16:25:28,367 - INFO - Successfully inserted image: 109696248_2828521930762418_277213041136568379_n.jpg
2025-07-23 16:25:28,367 - INFO - Processing image: 109670649_2828521987429079_4321599374349967381_n.jpg
2025-07-23 16:25:32,417 - INFO - Uploaded media file: 109670649_2828521987429079_4321599374349967381_n.jpg (ID: 1qH7ETZqvKk0yapNnwb9BN1woPKug0HXs)
2025-07-23 16:25:34,904 - INFO - Inserted image 109670649_2828521987429079_4321599374349967381_n.jpg into document
2025-07-23 16:25:34,904 - INFO - Successfully inserted image: 109670649_2828521987429079_4321599374349967381_n.jpg
2025-07-23 16:25:34,905 - INFO - Processing image: 109564725_2828521907429087_2940439975379453911_n.jpg
2025-07-23 16:25:39,045 - INFO - Uploaded media file: 109564725_2828521907429087_2940439975379453911_n.jpg (ID: 1V4VPF7qUp6T4UNENIhgVFVqnSZ_MjaRF)
2025-07-23 16:25:40,927 - INFO - Inserted image 109564725_2828521907429087_2940439975379453911_n.jpg into document
2025-07-23 16:25:40,927 - INFO - Successfully inserted image: 109564725_2828521907429087_2940439975379453911_n.jpg
2025-07-23 16:25:40,927 - INFO - Processing image: 109120824_2828521890762422_1242160168853690383_n.jpg
2025-07-23 16:25:44,983 - INFO - Uploaded media file: 109120824_2828521890762422_1242160168853690383_n.jpg (ID: 1Xxw1-agWVsfmMgyrT50ebalBs6ss6zZz)
2025-07-23 16:25:49,431 - INFO - Inserted image 109120824_2828521890762422_1242160168853690383_n.jpg into document
2025-07-23 16:25:49,431 - INFO - Successfully inserted image: 109120824_2828521890762422_1242160168853690383_n.jpg
2025-07-23 16:25:49,431 - INFO - Processing image: 109120214_2828521967429081_3017506504666285487_n.jpg
2025-07-23 16:25:54,320 - INFO - Uploaded media file: 109120214_2828521967429081_3017506504666285487_n.jpg (ID: 1EhkjRTz8ykQdxsr6xmE_2dKDqI3Cn6UF)
2025-07-23 16:25:56,940 - INFO - Inserted image 109120214_2828521967429081_3017506504666285487_n.jpg into document
2025-07-23 16:25:56,941 - INFO - Successfully inserted image: 109120214_2828521967429081_3017506504666285487_n.jpg
2025-07-23 16:25:56,941 - INFO - Syncing note: Kubernetes - K8S.md
2025-07-23 16:26:01,049 - INFO - Created Google Doc: Kubernetes - K8S (ID: 147Mq5UmA6RYioZHwEups6KmPviy63IXJ6XDbCgaQCkE)
2025-07-23 16:26:01,051 - INFO - Syncing note: Kho tài liệu sau 3 năm học IELTS của t - phần 1.md
2025-07-23 16:26:05,285 - INFO - Created Google Doc: Kho tài liệu sau 3 năm học IELTS của t - phần 1 (ID: 150xYMygGmwp2qRL17r9UTB0wzdQr6kdhaWCg-CIAsTo)
2025-07-23 16:26:05,285 - INFO - Syncing note: Cấu trúc dữ liệu & giải thuật.md
2025-07-23 16:26:08,914 - INFO - Created Google Doc: Cấu trúc dữ liệu & giải thuật (ID: 1h_zZ86WV-K__051oOS_44aZ2GTDEbp7boJEL5Huvu2Y)
2025-07-23 16:26:08,914 - INFO - Syncing note: Interview Senior Engineer.md
2025-07-23 16:26:13,212 - INFO - Created Google Doc: Interview Senior Engineer (ID: 1Lv1NA9RPUsNfFUcp5Yq6NrkgVNmaps_WO6xvdsaM9pg)
2025-07-23 16:26:13,212 - INFO - Syncing note: Inceptionlabs.md
2025-07-23 16:26:18,100 - INFO - Created Google Doc: Inceptionlabs (ID: 1tbAC0dEF-00NxpWyYXlVjHmfObxg4Og5cZ12rVZAsBc)
2025-07-23 16:26:18,101 - INFO - Syncing note: Linux.md
2025-07-23 16:26:21,874 - INFO - Created Google Doc: Linux (ID: 1k9vf8-_sXJdmZ0ETDdqfF8QBRbuKlCqcm_Q8FCS3onQ)
2025-07-23 16:26:21,874 - INFO - Processing image: Untitled 1 1.png
2025-07-23 16:26:25,887 - INFO - Uploaded media file: Untitled 1 1.png (ID: 1oG_H4CkZe2YGxUk715-3v3MdCO1Ba3Yw)
2025-07-23 16:26:28,893 - INFO - Inserted image Untitled 1 1.png into document
2025-07-23 16:26:28,894 - INFO - Successfully inserted image: Untitled 1 1.png
2025-07-23 16:26:28,894 - INFO - Syncing note: Testing.md
2025-07-23 16:26:32,386 - ERROR - Error creating Google Doc Testing: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1444Mb8gPGycZlXRiHmElvFPzEgR-atL-FMaVsoOZa8w:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[17].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[714].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[717].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[17].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[17].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[714].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[714].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[717].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[717].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:26:32,387 - INFO - Syncing note: Error handler.md
2025-07-23 16:26:35,744 - INFO - Created Google Doc: Error handler (ID: 1_MnERNZK3O52turL_vs6ukft78ztTQ8qJDgYwbgpbUA)
2025-07-23 16:26:35,744 - INFO - Syncing note: English with LLM - Số ít số nhiều.md
2025-07-23 16:26:39,409 - INFO - Created Google Doc: English with LLM - Số ít số nhiều (ID: 1zZ2XwtxN-a3C9hyuxoaDjmSDRTIoOTslSNec6x2fbGk)
2025-07-23 16:26:39,411 - INFO - Syncing note: Laravel.md
2025-07-23 16:26:42,325 - ERROR - Error creating Google Doc Laravel: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1rcvekEyFQMgmMlcUZkQXWc_EDXeYteNJddd-mE1RPv0:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[474].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[478].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[586].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[590].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[474].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[474].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[478].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[478].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[586].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[586].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[590].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[590].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:26:42,325 - INFO - Syncing note: Tự vệ.md
2025-07-23 16:26:45,733 - INFO - Created Google Doc: Tự vệ (ID: 1-8w9CuHFLqCcNhrCk3cJcUpO8JUml1bItSBASWDxiY8)
2025-07-23 16:26:45,734 - INFO - Syncing note: Vietop.md
2025-07-23 16:26:48,573 - ERROR - Error creating Google Doc Vietop: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1FtKL24N2oDRNkmt2jtsGcRHAAMcdcaLUndwfvaZ4pjQ:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[5].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[5].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[5].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:26:48,573 - INFO - Syncing note: Quy trình làm việc.md
2025-07-23 16:26:51,999 - INFO - Created Google Doc: Quy trình làm việc (ID: 1PiEpzyYWY_iLDPjD9WzlwyJAZFDvdFB95gwTiqGaSwQ)
2025-07-23 16:26:52,000 - INFO - Syncing note: Data structures & Algorithms.md
2025-07-23 16:26:55,314 - INFO - Created Google Doc: Data structures & Algorithms (ID: 1qmNusT8e6uZcD3OPVhsByJzlpWfueKpL2xo1HdVBZMQ)
2025-07-23 16:26:55,314 - INFO - Processing image: Pasted image 20240419132648.png
2025-07-23 16:26:59,412 - INFO - Uploaded media file: Pasted image 20240419132648.png (ID: 17QJ9_AR6DLUzY1N7sMQfIyLVtvYP-XZx)
2025-07-23 16:27:01,679 - INFO - Inserted image Pasted image 20240419132648.png into document
2025-07-23 16:27:01,679 - INFO - Successfully inserted image: Pasted image 20240419132648.png
2025-07-23 16:27:01,679 - INFO - Processing image: Algorithm.png
2025-07-23 16:27:07,135 - INFO - Uploaded media file: Algorithm.png (ID: 1f44pOGyeW6VaOeCvSFYLJQg5zsXz9nS3)
2025-07-23 16:27:09,452 - INFO - Inserted image Algorithm.png into document
2025-07-23 16:27:09,452 - INFO - Successfully inserted image: Algorithm.png
2025-07-23 16:27:09,452 - INFO - Processing image: Untitled 1.png
2025-07-23 16:27:14,492 - INFO - Uploaded media file: Untitled 1.png (ID: 1bRJbw31gFtTO-KlJt0GdC9nDQ3Lko_ub)
2025-07-23 16:27:16,875 - INFO - Inserted image Untitled 1.png into document
2025-07-23 16:27:16,877 - INFO - Successfully inserted image: Untitled 1.png
2025-07-23 16:27:16,877 - INFO - Processing image: 344761700_691730916042250_2784087986434747459_n.jpg
2025-07-23 16:27:21,246 - INFO - Uploaded media file: 344761700_691730916042250_2784087986434747459_n.jpg (ID: 1aDWBRveoIqYQRndZazQoh1R90poAlP03)
2025-07-23 16:27:23,473 - INFO - Inserted image 344761700_691730916042250_2784087986434747459_n.jpg into document
2025-07-23 16:27:23,474 - INFO - Successfully inserted image: 344761700_691730916042250_2784087986434747459_n.jpg
2025-07-23 16:27:23,474 - INFO - Syncing note: Inceptionlabs - Viclass.md
2025-07-23 16:27:28,508 - INFO - Created Google Doc: Inceptionlabs - Viclass (ID: 1tr6VqbjyS-rdFVlZStrjciLu7EaCO8U86LySe5iXyXU)
2025-07-23 16:27:28,508 - INFO - Syncing note: Wordpress.md
2025-07-23 16:27:32,384 - INFO - Created Google Doc: Wordpress (ID: 1WBtsxjhvfs47JIq3LEIowKek6Ea9PGFoIC-Il0GH44o)
2025-07-23 16:27:32,387 - INFO - Syncing note: Compiler.md
2025-07-23 16:27:36,855 - INFO - Created Google Doc: Compiler (ID: 1xGs6rKeXDyL0WtXfzynW4Yzz9Ukv6-phZKH5KDVqNTw)
2025-07-23 16:27:36,855 - INFO - Syncing note: Tổng hợp các nguồn ôn luyện thuật toán & Coding interview.md
2025-07-23 16:27:41,496 - INFO - Created Google Doc: Tổng hợp các nguồn ôn luyện thuật toán & Coding interview (ID: 1_SygAZcUzxRla_rMmvkBNUCe6M-eXJvfzm4dmmzYdlM)
2025-07-23 16:27:41,496 - INFO - Syncing note: Fashion - Tủ đồ - Quần áo.md
2025-07-23 16:27:45,076 - INFO - Created Google Doc: Fashion - Tủ đồ - Quần áo (ID: 1W7eZsLuDMSoQrm3PHiImtDHtRkYXCJhonj6I7YU7Htk)
2025-07-23 16:27:45,076 - INFO - Syncing note: Angular.md
2025-07-23 16:27:48,502 - INFO - Created Google Doc: Angular (ID: 14V0wyPwfKJ94bgyT7Wt8khHmVQbHNnPa5I8fD0SMIfM)
2025-07-23 16:27:48,503 - INFO - Syncing note: Nest.md
2025-07-23 16:27:53,191 - INFO - Created Google Doc: Nest (ID: 1YSuP4LkWehy42wHEiFIRLPvhRvVutjMqK3w1VCC6xf8)
2025-07-23 16:27:53,192 - INFO - Syncing note: Đi du lịch Hồ Chí Minh - Cần Giờ - Vũng Tàu.md
2025-07-23 16:27:57,589 - INFO - Created Google Doc: Đi du lịch Hồ Chí Minh - Cần Giờ - Vũng Tàu (ID: 1sR-pgXl1RQAtUFTuNHcBz_sp5mIVagWMvLnvTHBen3I)
2025-07-23 16:27:57,589 - INFO - Syncing note: English with LLM - Câu bị động.md
2025-07-23 16:28:00,873 - INFO - Created Google Doc: English with LLM - Câu bị động (ID: 12_sisehaPN1CYdofo_mN75umz8YOK84MwaV3QNqNZQs)
2025-07-23 16:28:00,873 - INFO - Syncing note: Mục lục thuật toán.md
2025-07-23 16:28:04,401 - INFO - Created Google Doc: Mục lục thuật toán (ID: 1GWFq4UXJnMZc2uzD7c1qkktHt1qlIpGaOVvbnpL-6N0)
2025-07-23 16:28:04,402 - INFO - Syncing note: VPC - Virtual Private Cloud - AZ - Availability Zone.md
2025-07-23 16:28:09,178 - INFO - Created Google Doc: VPC - Virtual Private Cloud - AZ - Availability Zone (ID: 1B-FcdLjGiBCgmBpLKHlEzX3gk7QrPaa_b0YxBpB6OT0)
2025-07-23 16:28:09,178 - INFO - Syncing note: Flutter.md
2025-07-23 16:28:13,066 - INFO - Created Google Doc: Flutter (ID: 1xWx5bWhqSugMfEKVWSONzX96pTGLoYOb7HP-MF4D0sI)
2025-07-23 16:28:13,067 - INFO - Syncing note: Some shit I need.md
2025-07-23 16:28:16,853 - INFO - Created Google Doc: Some shit I need (ID: 1JOQrIZvPLxsyRqI3sKXvkqaA-WBC1Lvii3LYKG1aKWg)
2025-07-23 16:28:16,853 - INFO - Syncing note: Câu hỏi phỏng vấn.md
2025-07-23 16:28:21,175 - INFO - Created Google Doc: Câu hỏi phỏng vấn (ID: 1hWz7vMe0GRHimiKAQHAADINMr8dOXp9j_5Z114AW9Qw)
2025-07-23 16:28:21,175 - INFO - Syncing note: Danh sách kiến thức chuẩn bị để phỏng vấn.md
2025-07-23 16:28:25,752 - INFO - Created Google Doc: Danh sách kiến thức chuẩn bị để phỏng vấn (ID: 16PJVGXIDl8_-_gN2U4Pqz5CzzDSkZhd7uQ2Ap0DQqlM)
2025-07-23 16:28:25,753 - INFO - Syncing note: Product management.md
2025-07-23 16:28:30,039 - INFO - Created Google Doc: Product management (ID: 14-5Wx_YebZx8em1j-SzaJgy2pTv1URUvqB5kMpBEUus)
2025-07-23 16:28:30,039 - INFO - Syncing note: Thanh toán chuyển khoản ngân hàng.md
2025-07-23 16:28:34,353 - INFO - Created Google Doc: Thanh toán chuyển khoản ngân hàng (ID: 1VP03gjdIjiNT9JUXQtE_P_QX11v-O8KaLfGry6X8hh4)
2025-07-23 16:28:34,353 - INFO - Syncing note: MCP - Model Context Protocol.md
2025-07-23 16:28:38,758 - INFO - Created Google Doc: MCP - Model Context Protocol (ID: 1vln4VCgRAOkkmNE_2HFiAtdS5l39AEGYg9pOI2X0A3E)
2025-07-23 16:28:38,759 - INFO - Syncing note: Java Microservices.md
2025-07-23 16:28:43,029 - INFO - Created Google Doc: Java Microservices (ID: 1Pf9dQJAK2BA1pl_qC75H9yCfB2bjDbUmrwmE_z1hLbo)
2025-07-23 16:28:43,029 - INFO - Syncing note: Cách giải các bài thuật toán.md
2025-07-23 16:28:47,578 - INFO - Created Google Doc: Cách giải các bài thuật toán (ID: 12rvgX-PgFhTUa4VDPrd0tJXN3SzkCMRwwxgWEJVB_ns)
2025-07-23 16:28:47,578 - INFO - Syncing note: Java.md
2025-07-23 16:28:52,312 - INFO - Created Google Doc: Java (ID: 1JlBhLTWvVpD3_rEbVJgm44GxPOAkB2HAg8sCqbUD1sM)
2025-07-23 16:28:52,312 - INFO - Syncing note: English with LLM - Mạo từ.md
2025-07-23 16:28:57,222 - INFO - Created Google Doc: English with LLM - Mạo từ (ID: 1sfcrpBFTbAj5hg1yiYBSAfVW6iySGmrqEejRaUaOOqM)
2025-07-23 16:28:57,222 - INFO - Syncing note: Logging.md
2025-07-23 16:29:00,890 - INFO - Created Google Doc: Logging (ID: 1ZGSSBYfIoe2FkHssbQEet_c-XnR7nxPDMQDIi8WXi20)
2025-07-23 16:29:00,890 - INFO - Syncing note: Microservices.md
2025-07-23 16:29:05,907 - INFO - Created Google Doc: Microservices (ID: 1oAI1vQbgyIpX5meTwlVfYpjFHiyzgBK55ZAIoxbbHvE)
2025-07-23 16:29:05,907 - INFO - Syncing note: IaaS.md
2025-07-23 16:29:10,370 - INFO - Created Google Doc: IaaS (ID: 1jh9lEkOUjZD1bgI-MlSAu-UHd34YBbVLKh4zdrOfbYA)
2025-07-23 16:29:10,373 - INFO - Syncing note: Golang Scheduler.md
2025-07-23 16:29:13,912 - INFO - Created Google Doc: Golang Scheduler (ID: 1BrK3S-U4a7FxKCCf3HObBfXxQuONyUHJ67ITgyIwNuI)
2025-07-23 16:29:13,912 - INFO - Syncing note: Solution sao lưu lịch sử chỉnh sửa.md
2025-07-23 16:29:17,055 - INFO - Created Google Doc: Solution sao lưu lịch sử chỉnh sửa (ID: 11rldC4yPRy99zEnkwbU7ErglbcDAhSqdxdbEcT_qupg)
2025-07-23 16:29:17,055 - INFO - Processing image: Pasted image 20240706134702.png
2025-07-23 16:29:22,037 - INFO - Uploaded media file: Pasted image 20240706134702.png (ID: 1oB8fhJka3_tA455y-dKfmHqI5wBw0TT3)
2025-07-23 16:29:24,791 - INFO - Inserted image Pasted image 20240706134702.png into document
2025-07-23 16:29:24,792 - INFO - Successfully inserted image: Pasted image 20240706134702.png
2025-07-23 16:29:24,792 - INFO - Syncing note: Postgresql.md
2025-07-23 16:29:29,265 - INFO - Created Google Doc: Postgresql (ID: 136ZfZ5SDvq859xOF4TRHHMSavPss6zlxgUyQ3oMtIEs)
2025-07-23 16:29:29,265 - INFO - Syncing note: Algorithms & Data Structures CheatSheet.md
2025-07-23 16:29:32,442 - ERROR - Error creating Google Doc Algorithms & Data Structures CheatSheet: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1SZHsP-OKhv-jkML1aNeenC4jeSAnJSeiV6K2J-43yQ8:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[5].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[24].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[38].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[51].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[58].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[61].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[74].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[88].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[98].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[103].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[106].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[110].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[125].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[127].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[132].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[134].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[138].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[143].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[158].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[169].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[191].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[194].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[198].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[240].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[244].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[246].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[250].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[252].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[256].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[258].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[264].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[268].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[274].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[290].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[294].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[298].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[302].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[322].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[329].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[341].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[360].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[363].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[367].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[370].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[387].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[391].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[395].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[413].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[415].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[5].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[5].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[24].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[24].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[38].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[38].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[51].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[51].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[58].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[58].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[61].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[61].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[74].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[74].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[88].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[88].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[98].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[98].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[103].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[103].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[106].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[106].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[110].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[110].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[125].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[125].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[127].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[127].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[132].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[132].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[134].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[134].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[138].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[138].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[143].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[143].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[158].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[158].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[169].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[169].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[191].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[191].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[194].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[194].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[198].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[198].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[240].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[240].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[244].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[244].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[246].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[246].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[250].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[250].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[252].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[252].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[256].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[256].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[258].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[258].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[264].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[264].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[268].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[268].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[274].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[274].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[290].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[290].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[294].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[294].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[298].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[298].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[302].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[302].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[322].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[322].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[329].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[329].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[341].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[341].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[360].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[360].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[363].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[363].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[367].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[367].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[370].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[370].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[387].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[387].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[391].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[391].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[395].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[395].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[413].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[413].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[415].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[415].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:29:32,443 - INFO - Syncing note: Node.js.md
2025-07-23 16:29:37,282 - INFO - Created Google Doc: Node.js (ID: 17jSk45dxEtwwas6zu_hCeQnMoukwF14CeZVvmrGRDAU)
2025-07-23 16:29:37,282 - INFO - Syncing note: HTTP - HTTPS - TLS - SSL.md
2025-07-23 16:29:41,558 - INFO - Created Google Doc: HTTP - HTTPS - TLS - SSL (ID: 1nUWq5aPTI24nOzbC4AUn8i3eDN99NpKY3PlGVsS7aT0)
2025-07-23 16:29:41,558 - INFO - Processing image: 095fcc4d-9e86-44a4-9592-eb1285145a64_800x564.jpg
2025-07-23 16:29:45,784 - INFO - Uploaded media file: 095fcc4d-9e86-44a4-9592-eb1285145a64_800x564.jpg (ID: 1bF8nYL2g76LVnV5eqf2MyxTdRpT2DqpC)
2025-07-23 16:29:48,455 - INFO - Inserted image 095fcc4d-9e86-44a4-9592-eb1285145a64_800x564.jpg into document
2025-07-23 16:29:48,456 - INFO - Successfully inserted image: 095fcc4d-9e86-44a4-9592-eb1285145a64_800x564.jpg
2025-07-23 16:29:48,456 - INFO - Processing image: 340771529_617175149885300_3812928478176216761_n.jpg
2025-07-23 16:29:52,966 - INFO - Uploaded media file: 340771529_617175149885300_3812928478176216761_n.jpg (ID: 1qFHo4zIJvYCWNkhAjaXEGHX4-kcLX3A4)
2025-07-23 16:29:56,232 - INFO - Inserted image 340771529_617175149885300_3812928478176216761_n.jpg into document
2025-07-23 16:29:56,245 - INFO - Successfully inserted image: 340771529_617175149885300_3812928478176216761_n.jpg
2025-07-23 16:29:56,245 - INFO - Syncing note: Fastpass.md
2025-07-23 16:30:08,518 - INFO - Created Google Doc: Fastpass (ID: 1RKNeWOOelxqRcWKSSQO4Dq1_V0vv3NhEwh2LTckVD4c)
2025-07-23 16:30:08,526 - INFO - Syncing note: Reflow, Repaint, Layout Shift là gì? Tối ưu để tránh CLS cao trong Core Web Vitals.md
2025-07-23 16:30:14,134 - ERROR - Error creating Google Doc Reflow, Repaint, Layout Shift là gì? Tối ưu để tránh CLS cao trong Core Web Vitals: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1eN0EV-L97__g4TA45McezucHhASikAtur_whwmpsRRY:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[27].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[42].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[62].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[74].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[78].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[82].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[88].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[91].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[96].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[102].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[27].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[27].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[42].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[42].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[62].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[62].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[74].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[74].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[78].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[78].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[82].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[82].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[88].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[88].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[91].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[91].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[96].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[96].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[102].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[102].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:30:14,160 - INFO - Syncing note: GraphQL.md
2025-07-23 16:30:19,722 - INFO - Created Google Doc: GraphQL (ID: 1XRPqGyW3mH-k23MYHKIYLZy4SkIwFPwTCBlZY1S53Es)
2025-07-23 16:30:19,723 - INFO - Syncing note: Freelance 2 - Justbijay.md
2025-07-23 16:30:23,095 - ERROR - Error creating Google Doc Freelance 2 - Justbijay: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1QruEGMg32zCLWhQKKr4PhXc-l047QgHJBf2jJTh7bFc:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[2].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[2].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[2].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:30:23,095 - INFO - Syncing note: Roadmap học tiếng Anh từ ChatGPT4.md
2025-07-23 16:30:29,083 - INFO - Created Google Doc: Roadmap học tiếng Anh từ ChatGPT4 (ID: 1HMUeRm_qtrIlRf3EqQD4z1pJpYJfmiEifBxP8nT9u8k)
2025-07-23 16:30:29,084 - INFO - Syncing note: Phỏng vấn JV-IT.md
2025-07-23 16:30:32,525 - INFO - Created Google Doc: Phỏng vấn JV-IT (ID: 1rX32Qx3mkh5SgOuNq5HgAPaTerWpLIGLyTARNFUDAok)
2025-07-23 16:30:32,526 - INFO - Syncing note: Skincare.md
2025-07-23 16:30:38,366 - INFO - Created Google Doc: Skincare (ID: 1gQyWzKQIleZqsJh9UWjTAyapAlki5hNWRuk4wQliYQk)
2025-07-23 16:30:38,367 - INFO - Syncing note: NAT port.md
2025-07-23 16:30:42,992 - INFO - Created Google Doc: NAT port (ID: 1QRdCDTzPcDXxqxX8WLeEvj_vZ_gx4rN1AuDwkROjMz8)
2025-07-23 16:30:42,992 - INFO - Syncing note: ElasticSearch.md
2025-07-23 16:30:46,964 - INFO - Created Google Doc: ElasticSearch (ID: 14BpjnJde-jqi0KPTBRwXmfWR0D3EblG2_6hL-34O6YI)
2025-07-23 16:30:46,965 - INFO - Syncing note: Các loại áo nên có trong tủ đồ.md
2025-07-23 16:30:51,009 - INFO - Created Google Doc: Các loại áo nên có trong tủ đồ (ID: 12NYzGsqaZe3M8I3ywZcKjpvWuIbuZu7XiN7arbkerEA)
2025-07-23 16:30:51,009 - INFO - Processing image: Untitled 9 1.png
2025-07-23 16:30:55,770 - INFO - Uploaded media file: Untitled 9 1.png (ID: 1MnHOMsbXlqkp9iKyVi7Q1hqtc5x_7Efs)
2025-07-23 16:30:58,922 - INFO - Inserted image Untitled 9 1.png into document
2025-07-23 16:30:58,922 - INFO - Successfully inserted image: Untitled 9 1.png
2025-07-23 16:30:58,923 - INFO - Processing image: Untitled 8 1.png
2025-07-23 16:31:03,321 - INFO - Uploaded media file: Untitled 8 1.png (ID: 18MqvhCSZqftxsWbYk1-HaWg6SoHAm5TV)
2025-07-23 16:31:06,318 - INFO - Inserted image Untitled 8 1.png into document
2025-07-23 16:31:06,318 - INFO - Successfully inserted image: Untitled 8 1.png
2025-07-23 16:31:06,318 - INFO - Processing image: Untitled 7 1.png
2025-07-23 16:31:11,001 - INFO - Uploaded media file: Untitled 7 1.png (ID: 1-6LNpdg8oY9FcuQkb5PIo79Ww7RVOvvh)
2025-07-23 16:31:13,528 - INFO - Inserted image Untitled 7 1.png into document
2025-07-23 16:31:13,528 - INFO - Successfully inserted image: Untitled 7 1.png
2025-07-23 16:31:13,528 - INFO - Processing image: Untitled 6 1.png
2025-07-23 16:31:18,329 - INFO - Uploaded media file: Untitled 6 1.png (ID: 1QyOUIh2Gb57JzvZiaQkO3jjZsxcELw4Z)
2025-07-23 16:31:20,793 - INFO - Inserted image Untitled 6 1.png into document
2025-07-23 16:31:20,793 - INFO - Successfully inserted image: Untitled 6 1.png
2025-07-23 16:31:20,793 - INFO - Processing image: Untitled 5 1.png
2025-07-23 16:31:24,645 - INFO - Uploaded media file: Untitled 5 1.png (ID: 1fbhyuPvVsxa2w1bBLRrqQwIupLKWxU-N)
2025-07-23 16:31:27,393 - INFO - Inserted image Untitled 5 1.png into document
2025-07-23 16:31:27,394 - INFO - Successfully inserted image: Untitled 5 1.png
2025-07-23 16:31:27,394 - INFO - Processing image: Untitled 4 1.png
2025-07-23 16:31:31,616 - INFO - Uploaded media file: Untitled 4 1.png (ID: 1KwT_c2Vp_qvoiJUGke3P93OglUcnx9n-)
2025-07-23 16:31:34,169 - INFO - Inserted image Untitled 4 1.png into document
2025-07-23 16:31:34,170 - INFO - Successfully inserted image: Untitled 4 1.png
2025-07-23 16:31:34,170 - INFO - Processing image: Untitled 3 3.png
2025-07-23 16:31:38,478 - INFO - Uploaded media file: Untitled 3 3.png (ID: 1-Zp3Kh1RheWOb9hDl04q1AdQ-nK8D8Qg)
2025-07-23 16:31:41,037 - INFO - Inserted image Untitled 3 3.png into document
2025-07-23 16:31:41,038 - INFO - Successfully inserted image: Untitled 3 3.png
2025-07-23 16:31:41,038 - INFO - Processing image: Untitled 2 3.png
2025-07-23 16:31:44,762 - INFO - Uploaded media file: Untitled 2 3.png (ID: 1eJwV6ZW4f_U9ZtWrT1uQCXM0n3SiwUW3)
2025-07-23 16:31:47,121 - INFO - Inserted image Untitled 2 3.png into document
2025-07-23 16:31:47,122 - INFO - Successfully inserted image: Untitled 2 3.png
2025-07-23 16:31:47,122 - INFO - Processing image: Untitled 1 6.png
2025-07-23 16:31:51,016 - INFO - Uploaded media file: Untitled 1 6.png (ID: 1NXTNTB77HoisGyoG5MGMWH81FZLXaN9h)
2025-07-23 16:31:53,341 - INFO - Inserted image Untitled 1 6.png into document
2025-07-23 16:31:53,341 - INFO - Successfully inserted image: Untitled 1 6.png
2025-07-23 16:31:53,341 - INFO - Processing image: Untitled 13.png
2025-07-23 16:31:58,046 - INFO - Uploaded media file: Untitled 13.png (ID: 11-5u7bSaOYRruIz3j7un0PCtjULRQqEE)
2025-07-23 16:32:00,046 - INFO - Inserted image Untitled 13.png into document
2025-07-23 16:32:00,046 - INFO - Successfully inserted image: Untitled 13.png
2025-07-23 16:32:00,047 - INFO - Syncing note: Kinh nghiệm deal lương.md
2025-07-23 16:32:03,629 - INFO - Created Google Doc: Kinh nghiệm deal lương (ID: 1yrrDWLFY_cK6XeGG3kWU5QFXko5ClbhuIGYN_37vzFU)
2025-07-23 16:32:03,629 - INFO - Syncing note: Quy tắc chọn thắt lưng.md
2025-07-23 16:32:08,211 - INFO - Created Google Doc: Quy tắc chọn thắt lưng (ID: 1Wsc3j1ZgMUvmg21qavJdG8BUvpIamGmUmODAC6ivWXU)
2025-07-23 16:32:08,211 - INFO - Syncing note: 37 Tips from a Senior Frontend Developer.md
2025-07-23 16:32:12,166 - INFO - Created Google Doc: 37 Tips from a Senior Frontend Developer (ID: 1B3Z7i15eKObkLQtQjA-6bICCzru0d7eSNjWKN23FUSM)
2025-07-23 16:32:12,166 - INFO - Syncing note: Troodonlabs.md
2025-07-23 16:32:15,018 - ERROR - Error creating Google Doc Troodonlabs: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/10r7EOtN2pfXkdi1e_0EyNx-AhwWOflUABNpVFgEpO8s:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[12].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[12].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[12].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:32:15,018 - INFO - Syncing note: ngosangns - home.md
2025-07-23 16:32:19,336 - INFO - Created Google Doc: ngosangns - home (ID: 1RaegnBgjEdlx6yd8wTZ4HCur8ET4aBw5C-p6nZOKhs0)
2025-07-23 16:32:19,336 - INFO - Syncing note: Interview - Senior Software Engineer (PHP, Javascript).md
2025-07-23 16:32:22,773 - INFO - Created Google Doc: Interview - Senior Software Engineer (PHP, Javascript) (ID: 1xNsWl2pGby1EV5ImDJA8NxX7dpCAXdXAZV5v3_elJG8)
2025-07-23 16:32:22,773 - INFO - Syncing note: Top 50 React interview quetions.md
2025-07-23 16:32:27,272 - INFO - Created Google Doc: Top 50 React interview quetions (ID: 1EVM5kG_QyiPXWEniW4h2G7yZwO0AS8bEdl_qMXlggDE)
2025-07-23 16:32:27,274 - INFO - Syncing note: Design.md
2025-07-23 16:32:31,679 - INFO - Created Google Doc: Design (ID: 1vp-eHzSYAVBJHWAgrcZ63U7FlEN3tj6fg_NahAoqBhQ)
2025-07-23 16:32:31,679 - INFO - Syncing note: Interview - Phỏng vấn.md
2025-07-23 16:32:36,301 - INFO - Created Google Doc: Interview - Phỏng vấn (ID: 1qRsWQBQxcWeQeDbABbDqEfSGDAlqB2ZfT66ktCWoYWs)
2025-07-23 16:32:36,302 - INFO - Syncing note: Frontend - Front-end.md
2025-07-23 16:32:39,427 - ERROR - Error creating Google Doc Frontend - Front-end: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1vi-pI3Y2WeH5jvKqB-NeQyex6C5tjY8sWI6Xw4nPrJU:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[130].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[130].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[130].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:32:39,428 - INFO - Syncing note: Javascript - Typescript.md
2025-07-23 16:32:43,874 - INFO - Created Google Doc: Javascript - Typescript (ID: 1yPfxlnMPAcZ7jTRZJiDih6xwPJ4k0qycbnX7aZEcEFQ)
2025-07-23 16:32:43,874 - INFO - Syncing note: Optimization.md
2025-07-23 16:32:47,346 - INFO - Created Google Doc: Optimization (ID: 12le8incIJINjhW89NsCT-NpVjmy9wRTsmQM5IrPP7d0)
2025-07-23 16:32:47,347 - INFO - Syncing note: Khóa học Golang scalable của Việt Trần.md
2025-07-23 16:32:51,947 - INFO - Created Google Doc: Khóa học Golang scalable của Việt Trần (ID: 1oq9Z3cAzH0D4cogdnSU_T7G2HJEyfFvQmVkJ6b3nqLw)
2025-07-23 16:32:51,948 - INFO - Syncing note: Các câu hỏi phỏng vấn VueJS.md
2025-07-23 16:32:55,117 - INFO - Created Google Doc: Các câu hỏi phỏng vấn VueJS (ID: 1xcR1raOq3WJtuaY5kENVGg35unYWe-Oy9zOnMRT-X8g)
2025-07-23 16:32:55,118 - INFO - Syncing note: Working day of users in a workspace.md
2025-07-23 16:32:59,635 - INFO - Created Google Doc: Working day of users in a workspace (ID: 1Lr7L0lQwJZ6WaGKY7iYt319TjWUiTj0gDL9Fm9L0DSo)
2025-07-23 16:32:59,635 - INFO - Syncing note: Backend - Back-end.md
2025-07-23 16:33:04,666 - INFO - Created Google Doc: Backend - Back-end (ID: 1hTbRriqsb6E7N9uSib7RNudSLVzInNcJFGYL1IJ4aWA)
2025-07-23 16:33:04,667 - INFO - Syncing note: Softskill - Kỹ năng mềm.md
2025-07-23 16:33:07,806 - INFO - Created Google Doc: Softskill - Kỹ năng mềm (ID: 1wmANYm4F2PmK7i5KeT1Uo7fUT8MgmszcoI2NN3lqSqE)
2025-07-23 16:33:07,806 - INFO - Syncing note: Severless.md
2025-07-23 16:33:11,191 - INFO - Created Google Doc: Severless (ID: 1bBCEj5hIUAcfuJyCkuuDOoH0yoQNebqLk9QKOxTwcvc)
2025-07-23 16:33:11,191 - INFO - Syncing note: AWS.md
2025-07-23 16:33:15,029 - INFO - Created Google Doc: AWS (ID: 1Ig8fLvanBs2n9DuXQ0P7fz7hWZLwuCMWwCtqXZEkmUw)
2025-07-23 16:33:15,029 - INFO - Processing image: Untitled.png
2025-07-23 16:33:19,957 - INFO - Uploaded media file: Untitled.png (ID: 1vaXWax87Bg-GzxGNu8B_fsqlWNjoC4lt)
2025-07-23 16:33:22,977 - INFO - Inserted image Untitled.png into document
2025-07-23 16:33:22,978 - INFO - Successfully inserted image: Untitled.png
2025-07-23 16:33:22,978 - INFO - Syncing note: Operation.md
2025-07-23 16:33:27,923 - INFO - Created Google Doc: Operation (ID: 17pxGH02iloBK4YhG16gOO-Fj5HE7p4T-jRuIihVXJPE)
2025-07-23 16:33:27,923 - INFO - Syncing note: English with LLM - Tense.md
2025-07-23 16:33:32,940 - INFO - Created Google Doc: English with LLM - Tense (ID: 1dQu8JcylCd9S3DfmOWwpYAw4_meb8zV29ubpM7ysjuA)
2025-07-23 16:33:32,942 - INFO - Syncing note: Bài 2 - Prompt Engineering, RAG và Finetuning.md
2025-07-23 16:33:37,401 - INFO - Created Google Doc: Bài 2 - Prompt Engineering, RAG và Finetuning (ID: 1Ov70nz_1DFC137fCafnIKAzYHrBz-TrvrSRGVJwFOus)
2025-07-23 16:33:37,402 - INFO - Syncing note: DevOps.md
2025-07-23 16:33:40,868 - INFO - Created Google Doc: DevOps (ID: 1a4KdMYA4KD234szbvm012vXly3_ktIa1icFpIyV7kPU)
2025-07-23 16:33:40,868 - INFO - Processing image: Untitled 4.png
2025-07-23 16:33:45,126 - INFO - Uploaded media file: Untitled 4.png (ID: 17uHirxDhdZBE2l2FKkzLRymVWqtm6R-E)
2025-07-23 16:33:47,800 - INFO - Inserted image Untitled 4.png into document
2025-07-23 16:33:47,801 - INFO - Successfully inserted image: Untitled 4.png
2025-07-23 16:33:47,801 - INFO - Syncing note: Nướng thịt ở nhà bác Cửu.md
2025-07-23 16:33:53,729 - INFO - Created Google Doc: Nướng thịt ở nhà bác Cửu (ID: 1g8xJQs49e-TJbenUqaFjhwiQb879OdSCbGzGhO7fFWo)
2025-07-23 16:33:53,730 - INFO - Syncing note: Google Cloud Platform - GCP.md
2025-07-23 16:34:00,516 - INFO - Created Google Doc: Google Cloud Platform - GCP (ID: 1Syq5VV46Q_jES1Hwb_TqGeNUdXuzJM1xpO7a74YWepI)
2025-07-23 16:34:00,517 - INFO - Syncing note: English with LLM - Câu gián tiếp.md
2025-07-23 16:34:04,796 - INFO - Created Google Doc: English with LLM - Câu gián tiếp (ID: 1FDtG_Wt2AFUKAk9wXXpeF_KsVjQxQe3YVLiTjowBhug)
2025-07-23 16:34:04,797 - INFO - Syncing note: Bảng màu gradient đẹp.md
2025-07-23 16:34:07,990 - INFO - Created Google Doc: Bảng màu gradient đẹp (ID: 1mVgcRX8-_2zJvcmAwkorhLwUxTn6vPzs-pVOvAb34dw)
2025-07-23 16:34:07,991 - INFO - Processing image: Pasted image 20240627101240.png
2025-07-23 16:34:13,451 - INFO - Uploaded media file: Pasted image 20240627101240.png (ID: 1hZKnmojK4iJyN9926guGu9WpLa2gBAWb)
2025-07-23 16:34:17,106 - INFO - Inserted image Pasted image 20240627101240.png into document
2025-07-23 16:34:17,108 - INFO - Successfully inserted image: Pasted image 20240627101240.png
2025-07-23 16:34:17,108 - INFO - Processing image: Pasted image 20240627101236.png
2025-07-23 16:34:22,355 - INFO - Uploaded media file: Pasted image 20240627101236.png (ID: 13_Aln3Z2T4RsPXRXEZU_TZkO0pcrYyln)
2025-07-23 16:34:25,359 - INFO - Inserted image Pasted image 20240627101236.png into document
2025-07-23 16:34:25,361 - INFO - Successfully inserted image: Pasted image 20240627101236.png
2025-07-23 16:34:25,361 - INFO - Processing image: Pasted image 20240627101232.png
2025-07-23 16:34:31,115 - INFO - Uploaded media file: Pasted image 20240627101232.png (ID: 1MZljvvS4nQ9wUfAMtvi04e1cZn9CahN7)
2025-07-23 16:34:34,529 - INFO - Inserted image Pasted image 20240627101232.png into document
2025-07-23 16:34:34,530 - INFO - Successfully inserted image: Pasted image 20240627101232.png
2025-07-23 16:34:34,530 - INFO - Processing image: Pasted image 20240627101228.png
2025-07-23 16:34:39,222 - INFO - Uploaded media file: Pasted image 20240627101228.png (ID: 182vyKcL8-Ak3-q4Jv0ZBe5I9JhBs9eDd)
2025-07-23 16:34:42,803 - INFO - Inserted image Pasted image 20240627101228.png into document
2025-07-23 16:34:42,803 - INFO - Successfully inserted image: Pasted image 20240627101228.png
2025-07-23 16:34:42,803 - INFO - Processing image: Pasted image 20240627101222.png
2025-07-23 16:34:47,738 - INFO - Uploaded media file: Pasted image 20240627101222.png (ID: 18_8iZotlU9ftTumM7Eb0gNHKAQA0i-fp)
2025-07-23 16:34:51,493 - INFO - Inserted image Pasted image 20240627101222.png into document
2025-07-23 16:34:51,493 - INFO - Successfully inserted image: Pasted image 20240627101222.png
2025-07-23 16:34:51,494 - INFO - Processing image: Pasted image 20240627101218.png
2025-07-23 16:34:56,260 - INFO - Uploaded media file: Pasted image 20240627101218.png (ID: 1ui3FKmAypDQhFdBCVfCEvjy6q7Qbzrzq)
2025-07-23 16:34:59,847 - INFO - Inserted image Pasted image 20240627101218.png into document
2025-07-23 16:34:59,848 - INFO - Successfully inserted image: Pasted image 20240627101218.png
2025-07-23 16:34:59,848 - INFO - Processing image: Pasted image 20240627101213.png
2025-07-23 16:35:05,116 - INFO - Uploaded media file: Pasted image 20240627101213.png (ID: 1RgurpN7CbW5cpibaxCgYHtZDhDQ16cB3)
2025-07-23 16:35:08,975 - INFO - Inserted image Pasted image 20240627101213.png into document
2025-07-23 16:35:08,975 - INFO - Successfully inserted image: Pasted image 20240627101213.png
2025-07-23 16:35:08,975 - INFO - Processing image: Pasted image 20240627101207.png
2025-07-23 16:35:14,025 - INFO - Uploaded media file: Pasted image 20240627101207.png (ID: 117RY6zL-xOVGhUcwHSEpqT89d-YrRvQv)
2025-07-23 16:35:17,580 - INFO - Inserted image Pasted image 20240627101207.png into document
2025-07-23 16:35:17,581 - INFO - Successfully inserted image: Pasted image 20240627101207.png
2025-07-23 16:35:17,581 - INFO - Syncing note: Cross-platform.md
2025-07-23 16:35:21,936 - INFO - Created Google Doc: Cross-platform (ID: 1usZV5gwpGxx6KJkmZALITXg3Mzg7qhxPGg6Yt56LsWk)
2025-07-23 16:35:21,936 - INFO - Syncing note: English with LLM - Cấu trúc câu.md
2025-07-23 16:35:25,396 - INFO - Created Google Doc: English with LLM - Cấu trúc câu (ID: 1lYEkZM9QsbxDHMW1AwFdWD5lsMJw9yW7obt1Hcl-2fA)
2025-07-23 16:35:25,396 - INFO - Syncing note: Git - Github.md
2025-07-23 16:35:28,330 - ERROR - Error creating Google Doc Git - Github: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1_QkBYMD0CQvdQQ2EfIJasmekdYAFm9zRhU2sAcO1Qv4:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[111].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[118].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[122].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[125].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[129].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[111].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[111].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[118].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[118].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[122].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[122].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[125].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[125].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[129].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[129].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:35:28,330 - INFO - Syncing note: Freelance 3 - Spa.md
2025-07-23 16:35:32,053 - ERROR - Error creating Google Doc Freelance 3 - Spa: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1nGEl1xisOoLRxaS1xDcvAnisndpJlX_23ihhG4pIpJk:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[53].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[59].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[63].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[69].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[73].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[53].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[53].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[59].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[59].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[63].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[63].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[69].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[69].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[73].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[73].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:35:32,054 - INFO - Syncing note: Các bước xây dựng ứng dụng.md
2025-07-23 16:35:36,226 - INFO - Created Google Doc: Các bước xây dựng ứng dụng (ID: 1BOzfxPoHcyZaVhtL2rZ82LYfRBg8SjOq9WdZ27zHXjQ)
2025-07-23 16:35:36,226 - INFO - Syncing note: Defer - Async - Inline, cách browser thực thi JavaScript.md
2025-07-23 16:35:39,017 - ERROR - Error creating Google Doc Defer - Async - Inline, cách browser thực thi JavaScript: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/12E1aPcUcswovs_3Xo2xyIb-oae7DtcybrUJrEy8a_R8:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[17].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[38].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[61].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[84].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[17].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[17].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[38].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[38].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[61].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[61].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[84].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[84].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:35:39,018 - INFO - Syncing note: Layered Design in Go - iRi.md
2025-07-23 16:35:43,456 - INFO - Created Google Doc: Layered Design in Go - iRi (ID: 1KxGyVWVSgl2L2bIKLc8iObM6ljy0AWR9CZs6xKrBlGQ)
2025-07-23 16:35:43,456 - INFO - Syncing note: English with LLM - Động từ nguyên mẫu và danh động từ.md
2025-07-23 16:35:47,857 - INFO - Created Google Doc: English with LLM - Động từ nguyên mẫu và danh động từ (ID: 1nZefHiTFzA5-roHOxahJP5pm9kHBToVBdg7vEGw5u6Q)
2025-07-23 16:35:47,858 - INFO - Syncing note: OS Scheduler.md
2025-07-23 16:35:52,792 - INFO - Created Google Doc: OS Scheduler (ID: 1Mu8l9iS3kQlXD5HliqJ1oW3Lg9YFEaZ_zXudjcY8uSU)
2025-07-23 16:35:52,793 - INFO - Syncing note: Game.md
2025-07-23 16:35:56,266 - INFO - Created Google Doc: Game (ID: 16KF6Blam-9im7sBLkuf-I96VRH65fr1a44GuLkhA93U)
2025-07-23 16:35:56,267 - INFO - Syncing note: Freelance 1 - Dropshipping.md
2025-07-23 16:35:59,684 - INFO - Created Google Doc: Freelance 1 - Dropshipping (ID: 16LLd9syZs1jhiY81M5UTawjptHEotCpADtRv7j5t_o0)
2025-07-23 16:35:59,684 - INFO - Processing image: Pasted image 20230826230101.png
2025-07-23 16:36:05,028 - INFO - Uploaded media file: Pasted image 20230826230101.png (ID: 18po0uoOe5eKio0rDbfJ18ybFd8SyDm0J)
2025-07-23 16:36:08,052 - INFO - Inserted image Pasted image 20230826230101.png into document
2025-07-23 16:36:08,052 - INFO - Successfully inserted image: Pasted image 20230826230101.png
2025-07-23 16:36:08,052 - INFO - Syncing note: Done in Troodonlabs.md
2025-07-23 16:36:11,156 - INFO - Created Google Doc: Done in Troodonlabs (ID: 1l-JKSZNi1a8IApp3FRSwwlFsyEEh9e0CxaWHggny2wU)
2025-07-23 16:36:11,156 - INFO - Syncing note: Note TOEIC Mỗi Ngày.md
2025-07-23 16:36:14,803 - INFO - Created Google Doc: Note TOEIC Mỗi Ngày (ID: 1kZtsoZ_LHR4TlHO2534vtB_XSOcHMvlHS-blDdWp5fU)
2025-07-23 16:36:14,803 - INFO - Syncing note: AI - Large Language Models (LLM).md
2025-07-23 16:36:20,082 - INFO - Created Google Doc: AI - Large Language Models (LLM) (ID: 1PFuhZBO8sEOl82dCZBC2_7NU8HsO26OoEZg2xryOO6Q)
2025-07-23 16:36:20,083 - INFO - Syncing note: Ứng tuyển.md
2025-07-23 16:36:24,366 - INFO - Created Google Doc: Ứng tuyển (ID: 19TMzNLGvIIQI2qjpBjDNWfeLzNKWrJECd2DKO_WKYmI)
2025-07-23 16:36:24,367 - INFO - Syncing note: Du lịch Trung Quốc.md
2025-07-23 16:36:28,196 - INFO - Created Google Doc: Du lịch Trung Quốc (ID: 1aZsKYyIWzPCC9SRWTEk2oTMDM8nye8i39As0Q2X6Y3o)
2025-07-23 16:36:28,196 - INFO - Syncing note: English with LLM - Thành ngữ.md
2025-07-23 16:36:32,424 - INFO - Created Google Doc: English with LLM - Thành ngữ (ID: 1-l7fX4EOlFR6JsvRlGvkmQ7qhHp6eIUSHpCXcBK4Ta0)
2025-07-23 16:36:32,425 - INFO - Syncing note: English with LLM - Câu điều kiện hỗn hợp.md
2025-07-23 16:36:35,821 - INFO - Created Google Doc: English with LLM - Câu điều kiện hỗn hợp (ID: 1YrsoFxJ-iGnWgr-fIIx5Co6Wz1qbP1L35hgb4aCd1q4)
2025-07-23 16:36:35,821 - INFO - Syncing note: C - C++.md
2025-07-23 16:36:39,701 - INFO - Created Google Doc: C - C++ (ID: 1BYAZs3Efvaeu_v1sBZQ_hhHDyGshAOIOMcPHQI--xjo)
2025-07-23 16:36:39,701 - INFO - Syncing note: Các loại vải phù hợp cho mùa hè.md
2025-07-23 16:36:43,278 - INFO - Created Google Doc: Các loại vải phù hợp cho mùa hè (ID: 12aJZaq2IQY9P9PeG_KUJ-6by4KN88l0CqtiUvUNnbf0)
2025-07-23 16:36:43,278 - INFO - Syncing note: MongoDB.md
2025-07-23 16:36:46,602 - INFO - Created Google Doc: MongoDB (ID: 1rdXefqHyFi_sRwZiUY_byaNE2ryAVWjYe_gl5ZTc5P8)
2025-07-23 16:36:46,602 - INFO - Syncing note: Laravel - Eloquent.md
2025-07-23 16:36:50,349 - INFO - Created Google Doc: Laravel - Eloquent (ID: 1_UjqJnZKad9pHZo6Byf3oFjD_3rLDXngwMSMR_y7ktc)
2025-07-23 16:36:50,349 - INFO - Syncing note: Security.md
2025-07-23 16:36:55,264 - INFO - Created Google Doc: Security (ID: 1NCF0HTSBMXoJ4OXsxTqJe4iJ91-C1Oi0pw6LE-AtHVM)
2025-07-23 16:36:55,265 - INFO - Syncing note: SaaS.md
2025-07-23 16:36:59,484 - INFO - Created Google Doc: SaaS (ID: 1OQ3bXftnMsY7iW29esKDWXrY0JY79fkGON0s1Sgs2qk)
2025-07-23 16:36:59,484 - INFO - Processing image: Fzo-pUoaEAApb8M.jpg
2025-07-23 16:37:03,536 - INFO - Uploaded media file: Fzo-pUoaEAApb8M.jpg (ID: 1C-4iby9zhPDac07sDSIhkST89oDcYdLp)
2025-07-23 16:37:06,295 - INFO - Inserted image Fzo-pUoaEAApb8M.jpg into document
2025-07-23 16:37:06,296 - INFO - Successfully inserted image: Fzo-pUoaEAApb8M.jpg
2025-07-23 16:37:06,296 - INFO - Syncing note: SEO Content.md
2025-07-23 16:37:09,686 - INFO - Created Google Doc: SEO Content (ID: 1xy0qkPWE3QnsxfTJtRjwPXzBb2yZ5I68-fkDGOng048)
2025-07-23 16:37:09,687 - INFO - Syncing note: Reflow, Repaint, Layout Shift là gì? Tối ưu để tránh CLS cao trong Core Web Vitals 2.md
2025-07-23 16:37:15,582 - INFO - Created Google Doc: Reflow, Repaint, Layout Shift là gì? Tối ưu để tránh CLS cao trong Core Web Vitals 2 (ID: 1WVUtBinsJnC7uShZEUjHpDSaWUp7BveFxmmbFAmacD4)
2025-07-23 16:37:15,583 - INFO - Syncing note: English with LLM - Mệnh đề trạng ngữ.md
2025-07-23 16:37:20,277 - INFO - Created Google Doc: English with LLM - Mệnh đề trạng ngữ (ID: 162HYmdLCgLkuJOecq0pI4OY9DfZ8tj05UvtBQ1pIdxw)
2025-07-23 16:37:20,277 - INFO - Syncing note: Concurrency - Parallel - Asynchronus - Multi-threading.md
2025-07-23 16:37:26,084 - INFO - Created Google Doc: Concurrency - Parallel - Asynchronus - Multi-threading (ID: 1zmalsE_PkzlNFYULoUu7LU6l76nLc3LQ6f2_m1_Pls0)
2025-07-23 16:37:26,084 - INFO - Processing image: Pasted image 20240120111541.png
2025-07-23 16:37:31,105 - INFO - Uploaded media file: Pasted image 20240120111541.png (ID: 1X50OSIdggIgUf7hiOVmuNgzkH5U1r23P)
2025-07-23 16:37:33,280 - INFO - Inserted image Pasted image 20240120111541.png into document
2025-07-23 16:37:33,280 - INFO - Successfully inserted image: Pasted image 20240120111541.png
2025-07-23 16:37:33,281 - INFO - Syncing note: LM Studio.md
2025-07-23 16:37:37,611 - INFO - Created Google Doc: LM Studio (ID: 1KpGQ-X2J9sn4P5dfGPFQlhy5fXiD28PBWhYBVmHgwtM)
2025-07-23 16:37:37,611 - INFO - Syncing note: Tools.md
2025-07-23 16:37:41,634 - INFO - Created Google Doc: Tools (ID: 1NIYt2NHsgHrNjkRoGZW8Wq4CY01zuzQaL1T-yKhe3ac)
2025-07-23 16:37:41,635 - INFO - Syncing note: Nén file.md
2025-07-23 16:37:44,883 - INFO - Created Google Doc: Nén file (ID: 1K-6ZKLAwkPcSLrryLJ0aFWSypayCPiu1o778eI4_jpo)
2025-07-23 16:37:44,883 - INFO - Syncing note: Note mẹ.md
2025-07-23 16:37:48,928 - INFO - Created Google Doc: Note mẹ (ID: 1Jei6QB0w7O9foYmvBFqGJykZTv1KgFLKinAxcWhzIps)
2025-07-23 16:37:48,928 - INFO - Syncing note: Database.md
2025-07-23 16:37:52,364 - ERROR - Error creating Google Doc Database: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1iom1K_c5Oobx4_OsDPzRA77eTakZPx0vHl5DMwTTjPE:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[456].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[486].update_text_style.text_style': Cannot find field.
Invalid JSON payload received. Unknown name "fontFamily" at 'requests[491].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[456].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[456].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[486].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[486].update_text_style.text_style\': Cannot find field.'}, {'field': 'requests[491].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[491].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:37:52,365 - INFO - Syncing note: Azure.md
2025-07-23 16:37:55,611 - INFO - Created Google Doc: Azure (ID: 1kw05Q8rA3VM8xploLb9_KYPxS4LMheg-6-m13khTTjQ)
2025-07-23 16:37:55,612 - INFO - Syncing folder: Excalidraw (4 notes)
2025-07-23 16:37:57,149 - INFO - Created folder: Excalidraw (ID: 1KZsRbh50V3BecKh2C5EJmna7uZOJnwwU)
2025-07-23 16:37:57,150 - INFO - Syncing note: Viclass - editor.geo.md
2025-07-23 16:38:00,144 - ERROR - Error creating Google Doc Viclass - editor.geo: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1DXjB3tylDqhNoJBNYaOVWcQEDNIOPp9VMrv1ns8Lqqk:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[14].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[14].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[14].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:38:00,145 - INFO - Syncing note: vocab.md
2025-07-23 16:38:03,199 - ERROR - Error creating Google Doc vocab: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1Qx-G3EtNsaYavO-OrJIRQNpux-gBQTKTRAOrZ1VBprU:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[60].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[60].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[60].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:38:03,199 - INFO - Syncing note: Viclass - 514 - Synchronize mouse position & mouse shape of the presenter.md
2025-07-23 16:38:06,826 - ERROR - Error creating Google Doc Viclass - 514 - Synchronize mouse position & mouse shape of the presenter: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1TfjzzglMl39r2ercHXXvodlBtlR4mXsKHANYy14aRNg:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[24].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[24].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[24].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:38:06,826 - INFO - Syncing note: Viclass - Load classroom coordinator states.md
2025-07-23 16:38:11,106 - ERROR - Error creating Google Doc Viclass - Load classroom coordinator states: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1dHdZaasb1XzyBbXDAjuuGI6SNJM_VqOX6kXdG7kQVfs:batchUpdate?alt=json returned "Invalid JSON payload received. Unknown name "fontFamily" at 'requests[20].update_text_style.text_style': Cannot find field.". Details: "[{'@type': 'type.googleapis.com/google.rpc.BadRequest', 'fieldViolations': [{'field': 'requests[20].update_text_style.text_style', 'description': 'Invalid JSON payload received. Unknown name "fontFamily" at \'requests[20].update_text_style.text_style\': Cannot find field.'}]}]">
2025-07-23 16:38:11,108 - INFO - Sync completed successfully!
