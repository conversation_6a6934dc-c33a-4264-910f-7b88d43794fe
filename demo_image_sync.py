#!/usr/bin/env python3
"""
Demo script để test tính năng sync hình ảnh từ Obsidian sang Google Docs
"""

import os
import tempfile
import shutil
from pathlib import Path

def create_demo_vault_with_images():
    """Tạo demo vault với hình ảnh để test"""
    print("🔧 Tạo demo Obsidian vault với hình ảnh...")
    
    # Tạo temporary directory
    temp_dir = tempfile.mkdtemp(prefix="obsidian_demo_")
    vault_path = Path(temp_dir)
    
    print(f"📁 Demo vault tạo tại: {vault_path}")
    
    # Tạo attachments folder
    attachments_dir = vault_path / "attachments"
    attachments_dir.mkdir()
    
    # Tạo dummy image files (text files để demo)
    image_files = [
        "diagram.png",
        "screenshot.jpg", 
        "chart.gif"
    ]
    
    for image_file in image_files:
        image_path = attachments_dir / image_file
        image_path.write_text(f"This is a dummy {image_file} file for demo purposes.\nIn real usage, this would be an actual image file.", encoding='utf-8')
        print(f"   ✅ Tạo dummy image: {image_file}")
    
    # Tạo markdown note với embedded images
    note_content = """# Demo Note với Hình ảnh

Đây là một note demo để test tính năng sync hình ảnh từ Obsidian sang Google Docs.

## Giới thiệu

Note này chứa nhiều hình ảnh được embed bằng syntax Obsidian.

## Diagram

Đây là một diagram quan trọng:

![[diagram.png]]

Diagram này minh họa kiến trúc hệ thống.

## Screenshot

Đây là screenshot của ứng dụng:

![[screenshot.jpg]]

Screenshot này cho thấy giao diện người dùng.

## Chart

Và đây là một biểu đồ:

![[chart.gif]]

Biểu đồ này hiển thị dữ liệu thống kê.

## Kết luận

Tất cả hình ảnh trên sẽ được:
1. Tự động tìm thấy trong thư mục attachments
2. Upload lên Google Drive
3. Chèn trực tiếp vào Google Doc

Quá trình này hoàn toàn tự động!
"""
    
    note_path = vault_path / "demo_with_images.md"
    note_path.write_text(note_content, encoding='utf-8')
    print(f"   ✅ Tạo demo note: demo_with_images.md")
    
    # Tạo subfolder với note khác
    subfolder = vault_path / "projects"
    subfolder.mkdir()
    
    project_note = """# Project Documentation

Đây là documentation cho project.

## Architecture

![[diagram.png]]

## UI Design

![[screenshot.jpg]]

Các hình ảnh này sẽ được tìm thấy từ thư mục attachments ở root level.
"""
    
    (subfolder / "project_docs.md").write_text(project_note, encoding='utf-8')
    print(f"   ✅ Tạo project note: projects/project_docs.md")
    
    return vault_path

def show_demo_instructions(vault_path):
    """Hiển thị hướng dẫn sử dụng demo"""
    print("\n" + "="*60)
    print("🎯 DEMO VAULT ĐÃ SẴN SÀNG!")
    print("="*60)
    
    print(f"\n📁 Demo vault location: {vault_path}")
    print("\n📋 Vault structure:")
    print("   demo_with_images.md     # Note chính với 3 hình ảnh")
    print("   projects/")
    print("   ├── project_docs.md     # Note trong subfolder")
    print("   attachments/")
    print("   ├── diagram.png         # Dummy image 1")
    print("   ├── screenshot.jpg      # Dummy image 2")
    print("   └── chart.gif           # Dummy image 3")
    
    print("\n🚀 Để test sync với Google Docs:")
    print("1. Cập nhật google_sync_config.json:")
    print(f'   "obsidian_path": "{vault_path}"')
    
    print("\n2. Đảm bảo bạn đã có credentials.json từ Google Cloud Console")
    
    print("\n3. Chạy sync:")
    print("   python obsidian_to_google_sync.py")
    print("   # hoặc")
    print("   ./run_google_sync.sh")
    
    print("\n✨ Kết quả mong đợi:")
    print("   - Tạo folder 'Obsidian Sync' trên Google Drive")
    print("   - Tạo subfolder 'Media' chứa 3 hình ảnh")
    print("   - Tạo 2 Google Docs với hình ảnh được chèn trực tiếp")
    print("   - Folder 'projects' với document tương ứng")
    
    print(f"\n🗑️  Để xóa demo vault:")
    print(f"   rm -rf {vault_path}")
    
    print("\n" + "="*60)

def main():
    print("🎨 Demo Obsidian to Google Docs Image Sync")
    print("=" * 50)
    
    # Kiểm tra xem module có tồn tại không
    try:
        from obsidian_to_google_sync import ObsidianToGoogleSync
        print("✅ Module obsidian_to_google_sync đã sẵn sàng")
    except ImportError:
        print("❌ Module obsidian_to_google_sync không tìm thấy")
        print("   Đảm bảo bạn đang chạy script từ đúng thư mục")
        return 1
    
    # Tạo demo vault
    vault_path = create_demo_vault_with_images()
    
    # Hiển thị hướng dẫn
    show_demo_instructions(vault_path)
    
    # Hỏi người dùng có muốn test ngay không
    print("\n❓ Bạn có muốn test sync ngay bây giờ? (y/n): ", end="")
    response = input().lower().strip()
    
    if response in ['y', 'yes', 'có']:
        print("\n🔧 Cập nhật config file...")
        
        # Cập nhật config file
        import json
        config_path = "google_sync_config.json"
        
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            config['obsidian_path'] = str(vault_path)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Đã cập nhật {config_path}")
            print("\n🚀 Bây giờ bạn có thể chạy:")
            print("   python obsidian_to_google_sync.py")
        else:
            print(f"❌ Không tìm thấy {config_path}")
            print("   Chạy: python obsidian_to_google_sync.py --create-config")
    else:
        print("\n👍 Demo vault đã sẵn sàng để bạn test sau!")
    
    return 0

if __name__ == "__main__":
    exit(main())
