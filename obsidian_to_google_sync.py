#!/usr/bin/env python3
"""
Obsidian to Google Docs/Drive Sync Script
Synchronizes notes and media from Obsidian vault to Google Docs and Google Drive
"""

import os
import re
import json
import mimetypes
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import logging
from datetime import datetime
import argparse

# Google API imports
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaFileUpload

import markdown
from bs4 import BeautifulSoup

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('google_sync.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ObsidianToGoogleSync:
    def __init__(self, obsidian_path: str, credentials_path: str):
        """
        Initialize the Google sync tool
        
        Args:
            obsidian_path: Path to Obsidian vault
            credentials_path: Path to Google OAuth2 credentials JSON file
        """
        self.obsidian_path = Path(obsidian_path)
        self.credentials_path = credentials_path
        self.creds = None
        self.drive_service = None
        self.docs_service = None
        
        # Google API scopes
        self.scopes = [
            'https://www.googleapis.com/auth/drive',
            'https://www.googleapis.com/auth/documents'
        ]
        
        # Supported media types
        self.supported_media = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg', '.pdf', '.mp4', '.mp3', '.wav'}
        
        # Folder mapping: obsidian_path -> google_drive_folder_id
        self.folder_mapping = {}
        
    def authenticate(self) -> bool:
        """Authenticate with Google APIs using OAuth2"""
        try:
            token_path = 'google_token.json'
            
            # Load existing token if available
            if os.path.exists(token_path):
                self.creds = Credentials.from_authorized_user_file(token_path, self.scopes)
            
            # If there are no (valid) credentials available, let the user log in
            if not self.creds or not self.creds.valid:
                if self.creds and self.creds.expired and self.creds.refresh_token:
                    self.creds.refresh(Request())
                else:
                    if not os.path.exists(self.credentials_path):
                        logger.error(f"Credentials file not found: {self.credentials_path}")
                        logger.error("Please download credentials.json from Google Cloud Console")
                        return False
                        
                    flow = InstalledAppFlow.from_client_secrets_file(
                        self.credentials_path, self.scopes)
                    self.creds = flow.run_local_server(port=0)
                
                # Save the credentials for the next run
                with open(token_path, 'w') as token:
                    token.write(self.creds.to_json())
            
            # Build service objects
            self.drive_service = build('drive', 'v3', credentials=self.creds)
            self.docs_service = build('docs', 'v1', credentials=self.creds)
            
            logger.info("Google authentication successful")
            return True
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return False
    
    def create_folder(self, name: str, parent_id: str = None) -> Optional[str]:
        """Create a folder in Google Drive"""
        try:
            folder_metadata = {
                'name': name,
                'mimeType': 'application/vnd.google-apps.folder'
            }
            
            if parent_id:
                folder_metadata['parents'] = [parent_id]
            
            folder = self.drive_service.files().create(
                body=folder_metadata,
                fields='id'
            ).execute()
            
            folder_id = folder.get('id')
            logger.info(f"Created folder: {name} (ID: {folder_id})")
            return folder_id
            
        except HttpError as e:
            logger.error(f"Error creating folder {name}: {e}")
            return None
    
    def find_or_create_folder(self, name: str, parent_id: str = None) -> Optional[str]:
        """Find existing folder or create new one"""
        try:
            # Search for existing folder
            query = f"name='{name}' and mimeType='application/vnd.google-apps.folder'"
            if parent_id:
                query += f" and '{parent_id}' in parents"
            
            results = self.drive_service.files().list(
                q=query,
                fields="files(id, name)"
            ).execute()
            
            items = results.get('files', [])
            
            if items:
                folder_id = items[0]['id']
                logger.info(f"Found existing folder: {name} (ID: {folder_id})")
                return folder_id
            else:
                return self.create_folder(name, parent_id)
                
        except HttpError as e:
            logger.error(f"Error finding/creating folder {name}: {e}")
            return None
    
    def upload_media_file(self, file_path: Path, folder_id: str) -> Optional[str]:
        """Upload a media file to Google Drive"""
        try:
            file_metadata = {
                'name': file_path.name,
                'parents': [folder_id]
            }
            
            mime_type = mimetypes.guess_type(str(file_path))[0] or 'application/octet-stream'
            media = MediaFileUpload(str(file_path), mimetype=mime_type)
            
            file = self.drive_service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id,webViewLink'
            ).execute()
            
            file_id = file.get('id')
            web_link = file.get('webViewLink')
            
            logger.info(f"Uploaded media file: {file_path.name} (ID: {file_id})")
            return file_id
            
        except HttpError as e:
            logger.error(f"Error uploading media file {file_path}: {e}")
            return None
    
    def get_obsidian_notes(self) -> List[Path]:
        """Get all markdown files from Obsidian vault"""
        notes = []
        
        if not self.obsidian_path.exists():
            logger.error(f"Obsidian path does not exist: {self.obsidian_path}")
            return notes
        
        # Find all .md files
        for md_file in self.obsidian_path.rglob("*.md"):
            # Skip hidden folders and files
            if any(part.startswith('.') for part in md_file.parts):
                continue
            notes.append(md_file)
        
        logger.info(f"Found {len(notes)} markdown files in Obsidian vault")
        return notes
    
    def organize_notes_by_folder(self, notes: List[Path]) -> Dict[str, List[Path]]:
        """Organize notes by their folder structure"""
        organized = {}
        
        for note in notes:
            # Get relative path from obsidian root
            rel_path = note.relative_to(self.obsidian_path)
            
            if len(rel_path.parts) == 1:
                # Root level file
                folder_name = "Root"
            else:
                # Use parent folder name
                folder_name = str(rel_path.parent)
            
            if folder_name not in organized:
                organized[folder_name] = []
            organized[folder_name].append(note)
        
        return organized
    
    def find_media_file(self, base_path: Path, media_name: str) -> Optional[Path]:
        """Find media file in common attachment folders"""
        search_folders = [
            base_path,
            base_path / "attachments",
            base_path / "assets", 
            base_path / "media",
            self.obsidian_path / "attachments",
            self.obsidian_path / "assets",
            self.obsidian_path / "media"
        ]
        
        for folder in search_folders:
            if folder.exists():
                media_path = folder / media_name
                if media_path.exists():
                    return media_path
        
        return None

    def convert_markdown_to_docs_requests(self, markdown_content: str, note_path: Path) -> List[Dict]:
        """Convert Obsidian markdown to Google Docs API requests"""
        try:
            # Convert basic markdown to HTML first
            html = markdown.markdown(markdown_content, extensions=['tables', 'fenced_code'])
            soup = BeautifulSoup(html, 'html.parser')

            requests = []
            index = 1  # Start after title

            # Process each element in the HTML
            for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'ul', 'ol', 'table', 'pre']):
                if element.name.startswith('h'):
                    # Handle headers
                    level = int(element.name[1])
                    text = element.get_text()

                    requests.append({
                        'insertText': {
                            'location': {'index': index},
                            'text': text + '\n'
                        }
                    })

                    # Apply heading style
                    requests.append({
                        'updateParagraphStyle': {
                            'range': {
                                'startIndex': index,
                                'endIndex': index + len(text)
                            },
                            'paragraphStyle': {
                                'namedStyleType': f'HEADING_{level}'
                            },
                            'fields': 'namedStyleType'
                        }
                    })

                    index += len(text) + 1

                elif element.name == 'p':
                    # Handle paragraphs
                    text = self.process_paragraph_formatting(element)
                    if text.strip():
                        requests.append({
                            'insertText': {
                                'location': {'index': index},
                                'text': text + '\n'
                            }
                        })
                        index += len(text) + 1

                elif element.name in ['ul', 'ol']:
                    # Handle lists
                    list_requests, list_length = self.process_list(element, index)
                    requests.extend(list_requests)
                    index += list_length

                elif element.name == 'table':
                    # Handle tables
                    table_requests, table_length = self.process_table(element, index)
                    requests.extend(table_requests)
                    index += table_length

                elif element.name == 'pre':
                    # Handle code blocks
                    code_text = element.get_text()
                    requests.append({
                        'insertText': {
                            'location': {'index': index},
                            'text': code_text + '\n'
                        }
                    })

                    # Apply monospace formatting
                    requests.append({
                        'updateTextStyle': {
                            'range': {
                                'startIndex': index,
                                'endIndex': index + len(code_text)
                            },
                            'textStyle': {
                                'fontFamily': 'Courier New',
                                'backgroundColor': {
                                    'color': {
                                        'rgbColor': {
                                            'red': 0.95,
                                            'green': 0.95,
                                            'blue': 0.95
                                        }
                                    }
                                }
                            },
                            'fields': 'fontFamily,backgroundColor'
                        }
                    })

                    index += len(code_text) + 1

            # Handle Obsidian-specific syntax
            requests = self.process_obsidian_syntax(requests, markdown_content, note_path)

            return requests

        except Exception as e:
            logger.error(f"Error converting markdown: {e}")
            return []

    def process_paragraph_formatting(self, element) -> str:
        """Process paragraph with inline formatting"""
        text = ""
        for content in element.contents:
            if hasattr(content, 'name'):
                if content.name == 'strong' or content.name == 'b':
                    text += content.get_text()
                elif content.name == 'em' or content.name == 'i':
                    text += content.get_text()
                elif content.name == 'code':
                    text += content.get_text()
                elif content.name == 'a':
                    text += content.get_text()
                else:
                    text += content.get_text()
            else:
                text += str(content)
        return text

    def process_list(self, element, start_index: int) -> Tuple[List[Dict], int]:
        """Process list elements"""
        requests = []
        total_length = 0

        for li in element.find_all('li'):
            text = li.get_text() + '\n'
            requests.append({
                'insertText': {
                    'location': {'index': start_index + total_length},
                    'text': text
                }
            })

            # Apply bullet list formatting
            requests.append({
                'createParagraphBullets': {
                    'range': {
                        'startIndex': start_index + total_length,
                        'endIndex': start_index + total_length + len(text) - 1
                    },
                    'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'
                }
            })

            total_length += len(text)

        return requests, total_length

    def process_table(self, element, start_index: int) -> Tuple[List[Dict], int]:
        """Process table elements (simplified - convert to text)"""
        requests = []
        table_text = ""

        for row in element.find_all('tr'):
            row_text = " | ".join([cell.get_text().strip() for cell in row.find_all(['td', 'th'])])
            table_text += row_text + '\n'

        requests.append({
            'insertText': {
                'location': {'index': start_index},
                'text': table_text + '\n'
            }
        })

        return requests, len(table_text) + 1

    def process_obsidian_syntax(self, requests: List[Dict], markdown_content: str, note_path: Path) -> List[Dict]:
        """Process Obsidian-specific syntax like [[links]] and ![[images]]"""
        # Handle embedded images ![[image.png]]
        embedded_image_pattern = r'!\[\[([^\]]+)\]\]'
        for match in re.finditer(embedded_image_pattern, markdown_content):
            image_name = match.group(1)
            image_path = self.find_media_file(note_path.parent, image_name)

            if image_path and image_path.exists():
                # Upload image to Drive and get shareable link
                # This would need to be handled in the main sync process
                logger.info(f"Found embedded image: {image_name}")

        return requests

    def create_google_doc(self, title: str, content_requests: List[Dict], folder_id: str) -> Optional[str]:
        """Create a Google Doc with formatted content"""
        try:
            # Create the document
            doc = {
                'title': title
            }

            document = self.docs_service.documents().create(body=doc).execute()
            doc_id = document.get('documentId')

            # Move document to the specified folder
            if folder_id:
                self.drive_service.files().update(
                    fileId=doc_id,
                    addParents=folder_id,
                    removeParents='root'
                ).execute()

            # Apply content formatting if there are requests
            if content_requests:
                self.docs_service.documents().batchUpdate(
                    documentId=doc_id,
                    body={'requests': content_requests}
                ).execute()

            logger.info(f"Created Google Doc: {title} (ID: {doc_id})")
            return doc_id

        except HttpError as e:
            logger.error(f"Error creating Google Doc {title}: {e}")
            return None

    def setup_folder_structure(self, root_folder_name: str = "Obsidian Sync") -> Optional[str]:
        """Setup the main folder structure in Google Drive"""
        try:
            # Create or find root folder
            root_folder_id = self.find_or_create_folder(root_folder_name)
            if not root_folder_id:
                return None

            # Create media subfolder
            media_folder_id = self.find_or_create_folder("Media", root_folder_id)
            if media_folder_id:
                self.folder_mapping['_media'] = media_folder_id

            self.folder_mapping['_root'] = root_folder_id
            return root_folder_id

        except Exception as e:
            logger.error(f"Error setting up folder structure: {e}")
            return None

    def sync_notes(self, root_folder_name: str = "Obsidian Sync") -> bool:
        """Main sync function"""
        try:
            logger.info("Starting Obsidian to Google Docs/Drive sync...")

            # Authenticate
            if not self.authenticate():
                return False

            # Setup folder structure
            root_folder_id = self.setup_folder_structure(root_folder_name)
            if not root_folder_id:
                return False

            # Get notes from Obsidian
            notes = self.get_obsidian_notes()
            if not notes:
                logger.warning("No notes found in Obsidian vault")
                return True

            # Organize notes by folder
            organized_notes = self.organize_notes_by_folder(notes)

            # Sync each folder
            for folder_name, folder_notes in organized_notes.items():
                logger.info(f"Syncing folder: {folder_name} ({len(folder_notes)} notes)")

                # Create folder in Google Drive
                if folder_name == "Root":
                    folder_id = root_folder_id
                else:
                    folder_id = self.find_or_create_folder(folder_name, root_folder_id)
                    if not folder_id:
                        logger.error(f"Failed to create folder: {folder_name}")
                        continue

                self.folder_mapping[folder_name] = folder_id

                # Sync notes in this folder
                for note_path in folder_notes:
                    self.sync_single_note(note_path, folder_id)

            logger.info("Sync completed successfully!")
            return True

        except Exception as e:
            logger.error(f"Sync failed: {e}")
            return False

    def sync_single_note(self, note_path: Path, folder_id: str) -> bool:
        """Sync a single note to Google Docs"""
        try:
            logger.info(f"Syncing note: {note_path.name}")

            # Read markdown content
            with open(note_path, 'r', encoding='utf-8') as f:
                markdown_content = f.read()

            # Convert to Google Docs format
            content_requests = self.convert_markdown_to_docs_requests(markdown_content, note_path)

            # Use filename (without extension) as title
            title = note_path.stem

            # Create Google Doc
            doc_id = self.create_google_doc(title, content_requests, folder_id)

            if doc_id:
                # Process any embedded media
                self.process_embedded_media(markdown_content, note_path, doc_id)
                return True

            return False

        except Exception as e:
            logger.error(f"Error syncing note {note_path}: {e}")
            return False

    def process_embedded_media(self, markdown_content: str, note_path: Path, doc_id: str):
        """Process and upload embedded media files"""
        try:
            # Find embedded images ![[image.png]]
            embedded_image_pattern = r'!\[\[([^\]]+)\]\]'

            for match in re.finditer(embedded_image_pattern, markdown_content):
                image_name = match.group(1)
                image_path = self.find_media_file(note_path.parent, image_name)

                if image_path and image_path.exists():
                    # Upload to media folder
                    media_folder_id = self.folder_mapping.get('_media')
                    if media_folder_id:
                        file_id = self.upload_media_file(image_path, media_folder_id)
                        if file_id:
                            logger.info(f"Uploaded media: {image_name} for note: {note_path.name}")

        except Exception as e:
            logger.error(f"Error processing embedded media: {e}")

    def create_config_file(self, config_path: str = "google_sync_config.json"):
        """Create a configuration file template"""
        config = {
            "obsidian_path": "./obsidian",
            "credentials_path": "credentials.json",
            "root_folder_name": "Obsidian Sync",
            "excluded_folders": [".obsidian", ".trash"],
            "excluded_files": ["*.tmp", "*.bak"]
        }

        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2)

        logger.info(f"Configuration file created: {config_path}")
        print(f"Please edit {config_path} and add your Google credentials.json file")


def main():
    parser = argparse.ArgumentParser(description="Sync Obsidian notes to Google Docs/Drive")
    parser.add_argument("--obsidian-path", default="./obsidian",
                       help="Path to Obsidian vault (default: ./obsidian)")
    parser.add_argument("--config", default="google_sync_config.json",
                       help="Configuration file path (default: google_sync_config.json)")
    parser.add_argument("--create-config", action="store_true",
                       help="Create a configuration file template")
    parser.add_argument("--credentials", default="credentials.json",
                       help="Google credentials file path (default: credentials.json)")
    parser.add_argument("--root-folder", default="Obsidian Sync",
                       help="Root folder name in Google Drive (default: Obsidian Sync)")

    args = parser.parse_args()

    if args.create_config:
        sync_tool = ObsidianToGoogleSync("", "")
        sync_tool.create_config_file(args.config)
        return

    # Load configuration
    if os.path.exists(args.config):
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)

        obsidian_path = config.get("obsidian_path", args.obsidian_path)
        credentials_path = config.get("credentials_path", args.credentials)
        root_folder_name = config.get("root_folder_name", args.root_folder)
    else:
        obsidian_path = args.obsidian_path
        credentials_path = args.credentials
        root_folder_name = args.root_folder

    if not os.path.exists(credentials_path):
        print(f"Google credentials file not found: {credentials_path}")
        print("Please download credentials.json from Google Cloud Console")
        print("Instructions:")
        print("1. Go to https://console.cloud.google.com/")
        print("2. Create a new project or select existing one")
        print("3. Enable Google Drive API and Google Docs API")
        print("4. Create credentials (OAuth 2.0 Client ID)")
        print("5. Download the JSON file and save as credentials.json")
        return

    # Initialize and run sync
    sync_tool = ObsidianToGoogleSync(obsidian_path, credentials_path)
    success = sync_tool.sync_notes(root_folder_name)

    if success:
        print("✅ Sync completed successfully!")
        print(f"Your notes have been uploaded to Google Drive in folder: {root_folder_name}")
    else:
        print("❌ Sync failed. Check the log for details.")


if __name__ == "__main__":
    main()
