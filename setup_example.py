#!/usr/bin/env python3
"""
Setup script to create example Obsidian vault for testing
"""

import os
from pathlib import Path

def create_example_vault():
    """Create an example Obsidian vault with sample notes"""
    
    # Create obsidian directory structure
    obsidian_path = Path("./obsidian")
    obsidian_path.mkdir(exist_ok=True)
    
    # Create folders
    folders = [
        "Daily Notes",
        "Projects", 
        "Resources",
        "attachments"
    ]
    
    for folder in folders:
        (obsidian_path / folder).mkdir(exist_ok=True)
    
    # Create sample notes
    notes = {
        "Welcome.md": """# Welcome to Obsidian Sync

This is a sample note to test the Obsidian to OneNote sync tool.

## Features
- **Bold text** and *italic text*
- Lists and bullet points
- Code blocks
- Links to other notes

## Code Example
```python
def hello_world():
    print("Hello from Obsidian!")
```

## Links
This note links to [[Project Alpha]] and [[Daily Note 2024-01-01]].

## Image
Here's an embedded image: ![[sample-image.png]]
""",
        
        "Daily Notes/Daily Note 2024-01-01.md": """# Daily Note - January 1, 2024

## Tasks
- [x] Set up Obsidian vault
- [ ] Test sync to OneNote
- [ ] Review project progress

## Notes
Today I'm testing the sync functionality. The tool should convert this markdown to HTML and create a page in OneNote.

## References
- [[Welcome]]
- [[Project Alpha]]
""",
        
        "Projects/Project Alpha.md": """# Project Alpha

## Overview
This is a sample project note with various markdown elements.

## Table Example
| Feature | Status | Priority |
|---------|--------|----------|
| Sync Notes | ✅ Done | High |
| Handle Images | 🔄 In Progress | High |
| Error Handling | ❌ Todo | Medium |

## Checklist
- [x] Initial setup
- [x] Basic markdown conversion
- [ ] Advanced features
- [ ] Testing

## Code Block
```javascript
const syncTool = {
    source: 'Obsidian',
    target: 'OneNote',
    status: 'active'
};
```

## Links
Back to [[Welcome]] or check [[Daily Notes/Daily Note 2024-01-01]].
""",
        
        "Resources/Markdown Guide.md": """# Markdown Syntax Guide

## Headers
# H1
## H2  
### H3

## Text Formatting
**Bold text**
*Italic text*
~~Strikethrough~~
`Inline code`

## Lists
### Unordered
- Item 1
- Item 2
  - Nested item
  - Another nested item

### Ordered
1. First item
2. Second item
3. Third item

## Links and References
- External link: [Google](https://google.com)
- Internal link: [[Welcome]]
- Image: ![[sample-image.png]]

## Blockquotes
> This is a blockquote
> It can span multiple lines

## Code Blocks
```python
def example_function():
    return "This is a code block"
```

## Tables
| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Row 1    | Data     | More data|
| Row 2    | Data     | More data|
"""
    }
    
    # Write sample notes
    for note_path, content in notes.items():
        full_path = obsidian_path / note_path
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    # Create a sample image placeholder
    sample_image_content = """This is a placeholder for sample-image.png
In a real scenario, this would be an actual image file.
The sync tool will look for this file in the attachments folder."""
    
    with open(obsidian_path / "attachments" / "sample-image.png.txt", 'w') as f:
        f.write(sample_image_content)
    
    # Create .obsidian config folder (to be ignored)
    obsidian_config = obsidian_path / ".obsidian"
    obsidian_config.mkdir(exist_ok=True)
    
    with open(obsidian_config / "workspace.json", 'w') as f:
        f.write('{"main":{"id":"workspace","type":"split","children":[]}}')
    
    print(f"✅ Example Obsidian vault created at: {obsidian_path.absolute()}")
    print(f"📁 Created {len(notes)} sample notes")
    print(f"📂 Created {len(folders)} folders")
    print("\nNext steps:")
    print("1. Run: python obsidian_to_onenote_sync.py --create-config")
    print("2. Edit sync_config.json with your Microsoft App credentials")
    print("3. Run: python obsidian_to_onenote_sync.py")

if __name__ == "__main__":
    create_example_vault()
