# Obsidian to OneNote Sync Makefile

.PHONY: help install setup-example create-config sync sync-google clean

help:
	@echo "Obsidian Sync Tools"
	@echo "Available commands:"
	@echo "  make -f sync.mk help          - Show this help message"
	@echo "  make -f sync.mk install       - Install Python dependencies"
	@echo "  make -f sync.mk setup-example - Create example Obsidian vault"
	@echo "  make -f sync.mk create-config - Create sync configuration file"
	@echo "  make -f sync.mk sync          - Run OneNote sync process"
	@echo "  make -f sync.mk sync-google   - Run Google Docs/Drive sync process"
	@echo "  make -f sync.mk clean         - Clean up generated files"

install:
	@echo "Installing Python dependencies..."
	pip3 install -r requirements.txt

setup-example:
	@echo "Creating example Obsidian vault..."
	python setup_example.py

create-config:
	@echo "Creating sync configuration file..."
	python obsidian_to_onenote_sync.py --create-config

sync:
	@echo "Starting Obsidian to OneNote sync..."
	python obsidian_to_onenote_sync.py

sync-google:
	@echo "Starting Obsidian to Google Docs/Drive sync..."
	python obsidian_to_google_sync.py

clean:
	@echo "Cleaning up generated files..."
	rm -rf obsidian/
	rm -f sync_config.json
	rm -f google_sync_config.json
	rm -f sync.log
	rm -f google_sync.log
	rm -f google_token.json
	rm -rf __pycache__/
	rm -f *.pyc

# Quick setup for first time users
quick-setup: install setup-example create-config
	@echo ""
	@echo "✅ Quick setup completed!"
	@echo ""
	@echo "Next steps:"
	@echo "1. Edit sync_config.json with your Microsoft App credentials"
	@echo "2. Run: make -f sync.mk sync"
