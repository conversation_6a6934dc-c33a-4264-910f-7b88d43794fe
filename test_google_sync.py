#!/usr/bin/env python3
"""
Test script for Obsidian to Google Docs/Drive sync module
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

def create_test_vault():
    """Create a temporary Obsidian vault for testing"""
    temp_dir = tempfile.mkdtemp(prefix="obsidian_test_")
    vault_path = Path(temp_dir)
    
    # Create test markdown files
    (vault_path / "test_note.md").write_text("""# Test Note

This is a test note with **bold** and *italic* text.

## Section 1

Here's a list:
- Item 1
- Item 2
- Item 3

## Section 2

Here's a code block:

```python
def hello_world():
    print("Hello, World!")
```

And a table:

| Column 1 | Column 2 |
|----------|----------|
| Value 1  | Value 2  |
| Value 3  | Value 4  |

## Links

This is an [[internal link]] and this is an [external link](https://example.com).
""", encoding='utf-8')
    
    # Create a subfolder with another note
    subfolder = vault_path / "subfolder"
    subfolder.mkdir()
    
    (subfolder / "subfolder_note.md").write_text("""# Subfolder Note

This note is in a subfolder.

It contains some content to test folder structure sync.
""", encoding='utf-8')
    
    # Create an attachments folder with a dummy image
    attachments = vault_path / "attachments"
    attachments.mkdir()
    
    # Create a dummy image file (just text for testing)
    (attachments / "test_image.png").write_text("This is a dummy image file for testing", encoding='utf-8')
    
    return vault_path

def test_module_import():
    """Test if the module can be imported"""
    try:
        from obsidian_to_google_sync import ObsidianToGoogleSync
        print("✅ Module import successful")
        return True
    except ImportError as e:
        print(f"❌ Module import failed: {e}")
        return False

def test_class_initialization():
    """Test if the class can be initialized"""
    try:
        from obsidian_to_google_sync import ObsidianToGoogleSync
        
        # Create test vault
        vault_path = create_test_vault()
        
        # Initialize sync tool (without credentials for testing)
        sync_tool = ObsidianToGoogleSync(str(vault_path), "dummy_credentials.json")
        
        print("✅ Class initialization successful")
        print(f"   Obsidian path: {sync_tool.obsidian_path}")
        print(f"   Credentials path: {sync_tool.credentials_path}")
        
        # Clean up
        shutil.rmtree(vault_path)
        return True
        
    except Exception as e:
        print(f"❌ Class initialization failed: {e}")
        return False

def test_note_discovery():
    """Test if notes can be discovered from vault"""
    try:
        from obsidian_to_google_sync import ObsidianToGoogleSync
        
        # Create test vault
        vault_path = create_test_vault()
        
        # Initialize sync tool
        sync_tool = ObsidianToGoogleSync(str(vault_path), "dummy_credentials.json")
        
        # Get notes
        notes = sync_tool.get_obsidian_notes()
        
        print(f"✅ Note discovery successful - found {len(notes)} notes")
        for note in notes:
            print(f"   - {note.relative_to(vault_path)}")
        
        # Test organization
        organized = sync_tool.organize_notes_by_folder(notes)
        print(f"✅ Note organization successful - {len(organized)} folders")
        for folder, folder_notes in organized.items():
            print(f"   - {folder}: {len(folder_notes)} notes")
        
        # Clean up
        shutil.rmtree(vault_path)
        return True
        
    except Exception as e:
        print(f"❌ Note discovery failed: {e}")
        return False

def test_markdown_conversion():
    """Test markdown to Google Docs conversion"""
    try:
        from obsidian_to_google_sync import ObsidianToGoogleSync
        
        # Create test vault
        vault_path = create_test_vault()
        
        # Initialize sync tool
        sync_tool = ObsidianToGoogleSync(str(vault_path), "dummy_credentials.json")
        
        # Test markdown conversion
        test_markdown = """# Test Header

This is **bold** and *italic* text.

## Subheader

- List item 1
- List item 2

```python
print("Hello World")
```
"""
        
        note_path = vault_path / "test.md"
        requests = sync_tool.convert_markdown_to_docs_requests(test_markdown, note_path)
        
        print(f"✅ Markdown conversion successful - generated {len(requests)} requests")
        
        # Clean up
        shutil.rmtree(vault_path)
        return True
        
    except Exception as e:
        print(f"❌ Markdown conversion failed: {e}")
        return False

def test_config_creation():
    """Test config file creation"""
    try:
        from obsidian_to_google_sync import ObsidianToGoogleSync
        
        sync_tool = ObsidianToGoogleSync("", "")
        
        # Create config in temp location
        temp_config = tempfile.mktemp(suffix=".json")
        sync_tool.create_config_file(temp_config)
        
        if os.path.exists(temp_config):
            print("✅ Config file creation successful")
            
            # Read and validate config
            import json
            with open(temp_config, 'r') as f:
                config = json.load(f)
            
            required_keys = ["obsidian_path", "credentials_path", "root_folder_name"]
            if all(key in config for key in required_keys):
                print("✅ Config file validation successful")
            else:
                print("❌ Config file missing required keys")
                return False
            
            # Clean up
            os.remove(temp_config)
            return True
        else:
            print("❌ Config file was not created")
            return False
            
    except Exception as e:
        print(f"❌ Config creation failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Obsidian to Google Docs/Drive Sync Module")
    print("=" * 60)
    
    tests = [
        ("Module Import", test_module_import),
        ("Class Initialization", test_class_initialization),
        ("Note Discovery", test_note_discovery),
        ("Markdown Conversion", test_markdown_conversion),
        ("Config Creation", test_config_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running test: {test_name}")
        print("-" * 40)
        
        if test_func():
            passed += 1
        else:
            print(f"❌ Test failed: {test_name}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
