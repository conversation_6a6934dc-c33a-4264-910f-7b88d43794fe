# Obsidian to OneNote Sync Tool

Công cụ Python để đồng bộ toà<PERSON> bộ notes và media từ Obsidian vault sang Microsoft OneNote.

## Tính năng

- ✅ Đồng bộ tất cả markdown files từ Obsidian
- ✅ Chuyển đổi Obsidian markdown syntax sang HTML cho OneNote
- ✅ Hỗ trợ embedded images và media files
- ✅ Tự động tạo notebook và sections dựa trên cấu trúc thư mục
- ✅ Xử lý Obsidian links `[[link]]`
- ✅ Hỗ trợ tables, code blocks, và formatting
- ✅ Logging chi tiết cho việc debug

## Yêu cầu hệ thống

- Python 3.7+
- Microsoft Azure App Registration (để truy cập OneNote API)
- Obsidian vault với markdown files

## Cài đặt

1. Clone hoặc download script
2. Cài đặt dependencies:

```bash
pip3 install -r requirements.txt
```

## Thiết lập Microsoft App

### Bước 1: Tạo Azure App Registration

1. <PERSON><PERSON><PERSON> cập [Azure Portal](https://portal.azure.com)
2. Vào **Azure Active Directory** > **App registrations**
3. Click **New registration**
4. Điền thông tin:
   - Name: `Obsidian OneNote Sync`
   - Supported account types: `Accounts in any organizational directory and personal Microsoft accounts`
   - Redirect URI: `Public client/native (mobile & desktop)` với value `http://localhost`

### Bước 2: Cấu hình permissions

1. Vào app vừa tạo > **API permissions**
2. Click **Add a permission** > **Microsoft Graph** > **Delegated permissions**
3. Thêm permissions:
   - `Notes.ReadWrite`
   - `offline_access`
4. Click **Grant admin consent**

### Bước 3: Lấy Client ID

1. Vào **Overview** tab
2. Copy **Application (client) ID**

## Sử dụng

### Bước 1: Tạo file cấu hình

```bash
python obsidian_to_onenote_sync.py --create-config
```

Lệnh này sẽ tạo file `sync_config.json`:

```json
{
  "obsidian_path": "./obsidian",
  "client_id": "YOUR_MICROSOFT_APP_CLIENT_ID",
  "client_secret": "YOUR_MICROSOFT_APP_CLIENT_SECRET",
  "notebook_name": "Obsidian Sync",
  "excluded_folders": [".obsidian", ".trash"],
  "excluded_files": ["*.tmp", "*.bak"]
}
```

### Bước 2: Cấu hình

Chỉnh sửa `sync_config.json`:

- `obsidian_path`: Đường dẫn đến Obsidian vault của bạn
- `client_id`: Client ID từ Azure App
- `client_secret`: Để trống (sử dụng device code flow)
- `notebook_name`: Tên notebook trong OneNote

### Bước 3: Chạy sync

```bash
python obsidian_to_onenote_sync.py
```

Script sẽ:

1. Yêu cầu bạn authenticate với Microsoft
2. Hiển thị URL và device code
3. Sau khi authenticate, bắt đầu sync

## Cấu trúc sync

```
Obsidian Vault/
├── folder1/
│   ├── note1.md
│   └── note2.md
├── folder2/
│   └── note3.md
└── root_note.md

↓ Sync to ↓

OneNote Notebook: "Obsidian Sync"
├── Section: "folder1"
│   ├── Page: "note1"
│   └── Page: "note2"
├── Section: "folder2"
│   └── Page: "note3"
└── Section: "Root"
    └── Page: "root_note"
```

## Supported Obsidian Features

### ✅ Được hỗ trợ

- Headers (`# ## ###`)
- **Bold** và _italic_ text
- Lists (ordered và unordered)
- Code blocks và inline code
- Tables
- Links `[[Internal Links]]`
- Embedded images `![[image.png]]`
- Line breaks và paragraphs

### ⚠️ Hỗ trợ một phần

- Tags (chuyển thành text)
- Math equations (chuyển thành code blocks)
- Mermaid diagrams (chuyển thành code blocks)

### ❌ Chưa hỗ trợ

- Canvas files
- Plugin-specific syntax
- Advanced Obsidian features (dataview, etc.)

## Troubleshooting

### Lỗi authentication

- Kiểm tra Client ID có đúng không
- Đảm bảo app permissions đã được grant
- Thử authenticate lại

### Lỗi không tìm thấy images

- Kiểm tra đường dẫn images trong markdown
- Đảm bảo images tồn tại trong vault
- Script tự động tìm trong các thư mục: `attachments/`, `assets/`, `media/`

### Lỗi tạo pages

- Kiểm tra kết nối internet
- Đảm bảo có quyền write vào OneNote
- Kiểm tra log file `sync.log`

## Command Line Options

```bash
# Sử dụng cơ bản
python obsidian_to_onenote_sync.py

# Chỉ định đường dẫn Obsidian khác
python obsidian_to_onenote_sync.py --obsidian-path "/path/to/vault"

# Sử dụng config file khác
python obsidian_to_onenote_sync.py --config "my_config.json"

# Chỉ định tên notebook
python obsidian_to_onenote_sync.py --notebook-name "My Notes"

# Tạo config file mới
python obsidian_to_onenote_sync.py --create-config
```

## Logs

Script tạo file `sync.log` với thông tin chi tiết về quá trình sync. Kiểm tra file này nếu gặp lỗi.

## Lưu ý quan trọng

1. **Backup**: Luôn backup Obsidian vault trước khi chạy
2. **Rate limiting**: OneNote API có giới hạn requests, script sẽ tự động retry
3. **Large files**: Files quá lớn có thể không sync được
4. **Unicode**: Script hỗ trợ đầy đủ Unicode/Vietnamese

## Phát triển thêm

Để thêm tính năng mới, có thể mở rộng:

- `convert_markdown_to_html()`: Xử lý syntax mới
- `sync_single_note()`: Logic sync custom
- `find_media_file()`: Tìm media files ở vị trí khác
